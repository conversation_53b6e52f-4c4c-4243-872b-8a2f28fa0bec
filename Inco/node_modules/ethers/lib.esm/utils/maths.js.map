{"version": 3, "file": "maths.js", "sourceRoot": "", "sources": ["../../src.ts/utils/maths.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AACH,OAAO,EAAE,OAAO,EAAE,WAAW,EAAE,MAAM,WAAW,CAAC;AACjD,OAAO,EAAE,MAAM,EAAE,cAAc,EAAE,MAAM,aAAa,CAAC;AAerD,MAAM,IAAI,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;AACvB,MAAM,IAAI,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;AAEvB,iDAAiD;AAGjD,uCAAuC;AACvC,MAAM,QAAQ,GAAG,gBAAgB,CAAC;AAElC;;;;;GAKG;AACH,MAAM,UAAU,QAAQ,CAAC,MAAoB,EAAE,MAAe;IAC1D,MAAM,KAAK,GAAG,OAAO,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;IACvC,MAAM,KAAK,GAAG,MAAM,CAAC,SAAS,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC,CAAC;IAEjD,MAAM,CAAC,CAAC,KAAK,IAAI,KAAK,CAAC,KAAK,IAAI,EAAE,UAAU,EAAE,eAAe,EAAE;QAC3D,SAAS,EAAE,UAAU,EAAE,KAAK,EAAE,UAAU,EAAE,KAAK,EAAE,MAAM;KAC1D,CAAC,CAAC;IAEH,yCAAyC;IACzC,IAAI,KAAK,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,EAAE;QACzB,MAAM,IAAI,GAAG,CAAC,IAAI,IAAI,KAAK,CAAC,GAAG,IAAI,CAAC;QACpC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;KACtC;IAED,OAAO,KAAK,CAAC;AACjB,CAAC;AAED;;;;;GAKG;AACH,MAAM,UAAU,MAAM,CAAC,MAAoB,EAAE,MAAe;IACxD,IAAI,KAAK,GAAG,SAAS,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;IACvC,MAAM,KAAK,GAAG,MAAM,CAAC,SAAS,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC,CAAC;IAEjD,MAAM,KAAK,GAAG,CAAC,IAAI,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,CAAC,CAAC;IAEvC,IAAI,KAAK,GAAG,IAAI,EAAE;QACd,KAAK,GAAG,CAAC,KAAK,CAAC;QACf,MAAM,CAAC,KAAK,IAAI,KAAK,EAAE,SAAS,EAAE,eAAe,EAAE;YAC/C,SAAS,EAAE,QAAQ,EAAE,KAAK,EAAE,UAAU,EAAE,KAAK,EAAE,MAAM;SACxD,CAAC,CAAC;QACH,MAAM,IAAI,GAAG,CAAC,IAAI,IAAI,KAAK,CAAC,GAAG,IAAI,CAAC;QACpC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC;KACnC;SAAM;QACH,MAAM,CAAC,KAAK,GAAG,KAAK,EAAE,UAAU,EAAE,eAAe,EAAE;YAC/C,SAAS,EAAE,QAAQ,EAAE,KAAK,EAAE,UAAU,EAAE,KAAK,EAAE,MAAM;SACxD,CAAC,CAAC;KACN;IAED,OAAO,KAAK,CAAC;AACjB,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,IAAI,CAAC,MAAoB,EAAE,KAAc;IACrD,MAAM,KAAK,GAAG,OAAO,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;IACvC,MAAM,IAAI,GAAG,MAAM,CAAC,SAAS,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC,CAAC;IAC9C,OAAO,KAAK,GAAG,CAAC,CAAC,IAAI,IAAI,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;AAC3C,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,SAAS,CAAC,KAAmB,EAAE,IAAa;IACxD,QAAQ,OAAM,CAAC,KAAK,CAAC,EAAE;QACnB,KAAK,QAAQ,CAAC,CAAC,OAAO,KAAK,CAAC;QAC5B,KAAK,QAAQ;YACT,cAAc,CAAC,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE,WAAW,EAAE,IAAI,IAAI,OAAO,EAAE,KAAK,CAAC,CAAC;YAC7E,cAAc,CAAC,KAAK,IAAI,CAAC,QAAQ,IAAI,KAAK,IAAI,QAAQ,EAAE,UAAU,EAAE,IAAI,IAAI,OAAO,EAAE,KAAK,CAAC,CAAC;YAC5F,OAAO,MAAM,CAAC,KAAK,CAAC,CAAC;QACzB,KAAK,QAAQ;YACT,IAAI;gBACA,IAAI,KAAK,KAAK,EAAE,EAAE;oBAAE,MAAM,IAAI,KAAK,CAAC,cAAc,CAAC,CAAC;iBAAE;gBACtD,IAAI,KAAK,CAAC,CAAC,CAAC,KAAK,GAAG,IAAI,KAAK,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;oBACtC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;iBACtC;gBACD,OAAO,MAAM,CAAC,KAAK,CAAC,CAAC;aACxB;YAAC,OAAM,CAAM,EAAE;gBACZ,cAAc,CAAC,KAAK,EAAE,gCAAiC,CAAC,CAAC,OAAQ,EAAE,EAAE,IAAI,IAAI,OAAO,EAAE,KAAK,CAAC,CAAC;aAChG;KACR;IACD,cAAc,CAAC,KAAK,EAAE,4BAA4B,EAAE,IAAI,IAAI,OAAO,EAAE,KAAK,CAAC,CAAC;AAChF,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,OAAO,CAAC,KAAmB,EAAE,IAAa;IACtD,MAAM,MAAM,GAAG,SAAS,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;IACtC,MAAM,CAAC,MAAM,IAAI,IAAI,EAAE,mCAAmC,EAAE,eAAe,EAAE;QACzE,KAAK,EAAE,UAAU,EAAE,SAAS,EAAE,SAAS,EAAE,KAAK;KACjD,CAAC,CAAC;IACH,OAAO,MAAM,CAAC;AAClB,CAAC;AAED,MAAM,OAAO,GAAG,kBAAkB,CAAC;AAEnC;;;GAGG;AACH,MAAM,UAAU,QAAQ,CAAC,KAAgC;IACrD,IAAI,KAAK,YAAY,UAAU,EAAE;QAC7B,IAAI,MAAM,GAAG,KAAK,CAAC;QACnB,KAAK,MAAM,CAAC,IAAI,KAAK,EAAE;YACnB,MAAM,IAAI,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;YAC1B,MAAM,IAAI,OAAO,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC;SAC/B;QACD,OAAO,MAAM,CAAC,MAAM,CAAC,CAAC;KACzB;IAED,OAAO,SAAS,CAAC,KAAK,CAAC,CAAC;AAC5B,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,SAAS,CAAC,KAAmB,EAAE,IAAa;IACxD,QAAQ,OAAM,CAAC,KAAK,CAAC,EAAE;QACnB,KAAK,QAAQ;YACT,cAAc,CAAC,KAAK,IAAI,CAAC,QAAQ,IAAI,KAAK,IAAI,QAAQ,EAAE,UAAU,EAAE,IAAI,IAAI,OAAO,EAAE,KAAK,CAAC,CAAC;YAC5F,OAAO,MAAM,CAAC,KAAK,CAAC,CAAC;QACzB,KAAK,QAAQ;YACT,cAAc,CAAC,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE,WAAW,EAAE,IAAI,IAAI,OAAO,EAAE,KAAK,CAAC,CAAC;YAC7E,cAAc,CAAC,KAAK,IAAI,CAAC,QAAQ,IAAI,KAAK,IAAI,QAAQ,EAAE,UAAU,EAAE,IAAI,IAAI,OAAO,EAAE,KAAK,CAAC,CAAC;YAC5F,OAAO,KAAK,CAAC;QACjB,KAAK,QAAQ;YACT,IAAI;gBACA,IAAI,KAAK,KAAK,EAAE,EAAE;oBAAE,MAAM,IAAI,KAAK,CAAC,cAAc,CAAC,CAAC;iBAAE;gBACtD,OAAO,SAAS,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC,CAAC;aACzC;YAAC,OAAM,CAAM,EAAE;gBACZ,cAAc,CAAC,KAAK,EAAE,2BAA4B,CAAC,CAAC,OAAQ,EAAE,EAAE,IAAI,IAAI,OAAO,EAAE,KAAK,CAAC,CAAC;aAC3F;KACR;IACD,cAAc,CAAC,KAAK,EAAE,uBAAuB,EAAE,IAAI,IAAI,OAAO,EAAE,KAAK,CAAC,CAAC;AAC3E,CAAC;AAGD;;;GAGG;AACH,MAAM,UAAU,QAAQ,CAAC,KAAgC;IACrD,OAAO,SAAS,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;AACtC,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,OAAO,CAAC,MAAoB,EAAE,MAAgB;IAC1D,MAAM,KAAK,GAAG,OAAO,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;IAEvC,IAAI,MAAM,GAAG,KAAK,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;IAEhC,IAAI,MAAM,IAAI,IAAI,EAAE;QAChB,qCAAqC;QACrC,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE;YAAE,MAAM,GAAG,GAAG,GAAG,MAAM,CAAC;SAAE;KACpD;SAAM;QACH,MAAM,KAAK,GAAG,SAAS,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;QACzC,MAAM,CAAC,KAAK,GAAG,CAAC,IAAI,MAAM,CAAC,MAAM,EAAE,wBAAyB,KAAM,SAAS,EAAE,eAAe,EAAE;YAC1F,SAAS,EAAE,SAAS;YACpB,KAAK,EAAE,UAAU;YACjB,KAAK,EAAE,MAAM;SAChB,CAAC,CAAC;QAEH,sCAAsC;QACtC,OAAO,MAAM,CAAC,MAAM,GAAG,CAAC,KAAK,GAAG,CAAC,CAAC,EAAE;YAAE,MAAM,GAAG,GAAG,GAAG,MAAM,CAAC;SAAE;KAEjE;IAED,OAAO,IAAI,GAAG,MAAM,CAAC;AACzB,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,SAAS,CAAC,MAAoB;IAC1C,MAAM,KAAK,GAAG,OAAO,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;IAEvC,IAAI,KAAK,KAAK,IAAI,EAAE;QAAE,OAAO,IAAI,UAAU,CAAC,EAAG,CAAC,CAAC;KAAE;IAEnD,IAAI,GAAG,GAAG,KAAK,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;IAC7B,IAAI,GAAG,CAAC,MAAM,GAAG,CAAC,EAAE;QAAE,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;KAAE;IAExC,MAAM,MAAM,GAAG,IAAI,UAAU,CAAC,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;IAC9C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACpC,MAAM,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC;QACrB,MAAM,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,MAAM,EAAE,MAAM,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;KAC/D;IAED,OAAO,MAAM,CAAC;AAClB,CAAC;AAED;;;;;;GAMG;AACH,MAAM,UAAU,UAAU,CAAC,KAA+B;IACtD,IAAI,MAAM,GAAG,OAAO,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAA,CAAC,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;IAChF,OAAO,MAAM,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE;QAAE,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;KAAE;IAChE,IAAI,MAAM,KAAK,EAAE,EAAE;QAAE,MAAM,GAAG,GAAG,CAAC;KAAE;IACpC,OAAO,IAAI,GAAG,MAAM,CAAC;AACzB,CAAC"}