{"version": 3, "file": "plugins-network.js", "sourceRoot": "", "sources": ["../../src.ts/providers/plugins-network.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,gBAAgB,EAAE,MAAM,wBAAwB,CAAC;AAE1D,OAAO,EAAE,cAAc,EAAE,MAAM,mBAAmB,CAAC;AAMnD,MAAM,UAAU,GAAG,4CAA4C,CAAC;AAEhE;;GAEG;AACH,MAAM,OAAO,aAAa;IACtB;;;;;OAKG;IACM,IAAI,CAAU;IAEvB;;OAEG;IACH,YAAY,IAAY;QACpB,gBAAgB,CAAgB,IAAI,EAAE,EAAE,IAAI,EAAE,CAAC,CAAC;IACpD,CAAC;IAED;;OAEG;IACH,KAAK;QACD,OAAO,IAAI,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACxC,CAAC;CAKJ;AAsCD;;;GAGG;AACH,MAAM,OAAO,aAAc,SAAQ,aAAa;IAC5C;;;;;OAKG;IACM,cAAc,CAAU;IAEjC;;OAEG;IACM,MAAM,CAAU;IAEzB;;OAEG;IACM,QAAQ,CAAU;IAE3B;;OAEG;IACM,UAAU,CAAU;IAE7B;;OAEG;IACM,aAAa,CAAU;IAEhC;;OAEG;IACM,sBAAsB,CAAU;IAEzC;;OAEG;IACM,mBAAmB,CAAU;IAGtC;;;;OAIG;IACH,YAAY,cAAuB,EAAE,KAAyB;QAC1D,IAAI,cAAc,IAAI,IAAI,EAAE;YAAE,cAAc,GAAG,CAAC,CAAC;SAAE;QACnD,KAAK,CAAC,sCAAuC,CAAC,cAAc,IAAI,CAAC,CAAE,EAAE,CAAC,CAAC;QAEvE,MAAM,KAAK,GAA2B,EAAE,cAAc,EAAE,CAAC;QACzD,SAAS,GAAG,CAAC,IAA6B,EAAE,OAAe;YACvD,IAAI,KAAK,GAAG,CAAC,KAAK,IAAI,EAAG,CAAC,CAAC,IAAI,CAAC,CAAC;YACjC,IAAI,KAAK,IAAI,IAAI,EAAE;gBAAE,KAAK,GAAG,OAAO,CAAC;aAAE;YACvC,cAAc,CAAC,OAAM,CAAC,KAAK,CAAC,KAAK,QAAQ,EAAE,qBAAsB,IAAK,EAAE,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;YAC1F,KAAK,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC;QACxB,CAAC;QAED,GAAG,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;QACrB,GAAG,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;QACvB,GAAG,CAAC,YAAY,EAAE,CAAC,CAAC,CAAC;QACrB,GAAG,CAAC,eAAe,EAAE,EAAE,CAAC,CAAC;QACzB,GAAG,CAAC,wBAAwB,EAAE,IAAI,CAAC,CAAC;QACpC,GAAG,CAAC,qBAAqB,EAAE,IAAI,CAAC,CAAC;QAEjC,gBAAgB,CAAgB,IAAI,EAAE,KAAK,CAAC,CAAC;IACjD,CAAC;IAED,KAAK;QACD,OAAO,IAAI,aAAa,CAAC,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,CAAC;IACxD,CAAC;CACJ;AAED;;;;;;;;GAQG;AACH,MAAM,OAAO,SAAU,SAAQ,aAAa;IAExC;;OAEG;IACM,OAAO,CAAU;IAE1B;;OAEG;IACM,aAAa,CAAU;IAEhC;;;;OAIG;IACH,YAAY,OAAuB,EAAE,aAA6B;QAC9D,KAAK,CAAC,gCAAgC,CAAC,CAAC;QACxC,gBAAgB,CAAY,IAAI,EAAE;YAC9B,OAAO,EAAE,CAAC,OAAO,IAAI,UAAU,CAAC;YAChC,aAAa,EAAE,CAAC,CAAC,aAAa,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA,CAAC,CAAC,aAAa,CAAC;SAC9D,CAAC,CAAC;IACP,CAAC;IAED,KAAK;QACD,OAAO,IAAI,SAAS,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;IAC3D,CAAC;CACJ;AAED;;;;;;GAMG;AACH,MAAM,OAAO,oBAAqB,SAAQ,aAAa;IAC1C,YAAY,CAA2C;IAEhE;;OAEG;IACH,IAAI,WAAW;QACX,OAAO,IAAI,CAAC,YAAY,CAAC;IAC7B,CAAC;IAED;;OAEG;IACH,YAAY,WAAqD;QAC7D,KAAK,CAAC,oCAAoC,CAAC,CAAC;QAC5C,IAAI,CAAC,YAAY,GAAG,WAAW,CAAC;IACpC,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,UAAU,CAAC,QAAkB;QAC/B,OAAO,MAAM,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;IAC7C,CAAC;IAED,KAAK;QACD,OAAO,IAAI,oBAAoB,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;IACvD,CAAC;CACJ;AAED,MAAM,OAAO,4BAA6B,SAAQ,aAAa;IAClD,IAAI,CAAS;IACb,YAAY,CAAyK;IAE9L;;OAEG;IACH,IAAI,GAAG,KAAa,OAAO,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;IAEvC;;OAEG;IACH,IAAI,WAAW,KAA6K,OAAO,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC;IAEvN;;;OAGG;IACH,YAAY,GAAW,EAAE,WAAmL;QACxM,KAAK,CAAC,kDAAkD,CAAC,CAAC;QAC1D,IAAI,CAAC,IAAI,GAAG,GAAG,CAAC;QAChB,IAAI,CAAC,YAAY,GAAG,WAAW,CAAC;IACpC,CAAC;IAED,qDAAqD;IACrD,KAAK,KAAmC,OAAO,IAAI,CAAC,CAAC,CAAC;CACzD;AAED;;;;;;;;;;;;;;;;;;;;;;;EAuBE"}