{"version": 3, "file": "utils.js", "sourceRoot": "", "sources": ["../../src.ts/wallet/utils.ts"], "names": [], "mappings": "AAAA;;GAEG;AAEH,OAAO,EACH,YAAY,EAAE,cAAc,EAAE,WAAW,EAC5C,MAAM,mBAAmB,CAAC;AAE3B,MAAM,UAAU,aAAa,CAAC,SAAiB;IAC3C,IAAI,OAAM,CAAC,SAAS,CAAC,KAAK,QAAQ,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE;QAC/D,SAAS,GAAG,IAAI,GAAG,SAAS,CAAC;KAChC;IACD,OAAO,YAAY,CAAC,SAAS,CAAC,CAAC;AACnC,CAAC;AAED,MAAM,UAAU,IAAI,CAAC,KAAsB,EAAE,MAAc;IACvD,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;IACtB,OAAO,KAAK,CAAC,MAAM,GAAG,MAAM,EAAE;QAAE,KAAK,GAAG,GAAG,GAAG,KAAK,CAAC;KAAE;IACtD,OAAO,KAAK,CAAC;AACjB,CAAC;AAED,MAAM,UAAU,WAAW,CAAC,QAA6B;IACrD,IAAI,OAAM,CAAC,QAAQ,CAAC,KAAK,QAAQ,EAAE;QAC/B,OAAO,WAAW,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;KACxC;IACD,OAAO,YAAY,CAAC,QAAQ,CAAC,CAAC;AAClC,CAAC;AAED,MAAM,UAAU,OAAO,CAAI,MAAW,EAAE,KAAa;IAEjD,MAAM,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,oCAAoC,CAAC,CAAC;IAChE,cAAc,CAAC,KAAK,IAAI,IAAI,EAAE,cAAc,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;IAE7D,MAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;IACtB,MAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;IACtB,MAAM,IAAI,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC;IAEhC,IAAI,GAAG,GAAG,MAAM,CAAC;IACjB,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE;QAE9C,iEAAiE;QACjE,IAAI,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;YACpB,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,EAAE;gBAAE,MAAM;aAAE;YACvC,GAAG,GAAG,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;SAE7B;aAAM,IAAI,OAAM,CAAC,GAAG,CAAC,KAAK,QAAQ,EAAE;YACjC,IAAI,KAAK,GAAQ,IAAI,CAAC;YACtB,KAAK,MAAM,GAAG,IAAI,GAAG,EAAE;gBAClB,IAAI,GAAG,CAAC,WAAW,EAAE,KAAK,IAAI,EAAE;oBAC5B,KAAK,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;oBACjB,MAAM;iBACT;aACL;YACD,GAAG,GAAG,KAAK,CAAC;SAEf;aAAM;YACH,GAAG,GAAG,IAAI,CAAC;SACd;QAED,IAAI,GAAG,IAAI,IAAI,EAAE;YAAE,MAAM;SAAE;KAC9B;IAED,cAAc,CAAC,CAAC,IAAI,IAAI,GAAG,IAAI,IAAI,EAAE,wBAAwB,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;IAE7E,IAAI,IAAI,IAAI,GAAG,IAAI,IAAI,EAAE;QACrB,IAAI,IAAI,KAAK,KAAK,EAAE;YAChB,IAAI,OAAM,CAAC,GAAG,CAAC,KAAK,QAAQ,IAAI,GAAG,CAAC,KAAK,CAAC,YAAY,CAAC,EAAE;gBACrD,OAAmB,QAAQ,CAAC,GAAG,CAAC,CAAC;aACpC;iBAAM,IAAI,MAAM,CAAC,aAAa,CAAC,GAAG,CAAC,EAAE;gBAClC,OAAO,GAAG,CAAC;aACd;SACJ;QAED,IAAI,IAAI,KAAK,QAAQ,EAAE;YACnB,IAAI,OAAM,CAAC,GAAG,CAAC,KAAK,QAAQ,IAAI,GAAG,CAAC,KAAK,CAAC,aAAa,CAAC,EAAE;gBACtD,OAAmB,UAAU,CAAC,GAAG,CAAC,CAAC;aACtC;SACJ;QAED,IAAI,IAAI,KAAK,MAAM,EAAE;YACjB,IAAI,OAAM,CAAC,GAAG,CAAC,KAAK,QAAQ,EAAE;gBAAE,OAAmB,aAAa,CAAC,GAAG,CAAC,CAAC;aAAE;SAC3E;QAED,IAAI,IAAI,KAAK,OAAO,IAAI,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;YAAE,OAAmB,GAAG,CAAC;SAAE;QACvE,IAAI,IAAI,KAAK,OAAM,CAAC,GAAG,CAAC,EAAE;YAAE,OAAO,GAAG,CAAC;SAAE;QAEzC,cAAc,CAAC,KAAK,EAAE,wBAAyB,IAAK,GAAG,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;KAC1E;IAED,OAAO,GAAG,CAAC;AACf,CAAC;AACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EA8BE;AACF,0DAA0D;AAC1D;;;;;;;;;;;;;;;;;;;;;;;EAuBE"}