flf2a? 4 3 9 0 12 0 64 0
Name   : Heart Right
Author : <PERSON><PERSON>
Date   : 2004/1/15 15:41:27
Version: 1.0
-------------------------------------------------

-------------------------------------------------
This font has been created using JavE's FIGlet font export assistant.
Have a look at: http://www.jave.de

Permission is hereby given to modify this font, as long as the
modifier's name is placed on a comment line.
      
.-.-. 
'._.' 
      
.-.-.  
'. ! ) 
  ).'  
       
.-.-.  
'. " ) 
  ).'  
       
.-.-.  
'. # ) 
  ).'  
       
.-.-.  
'. $ ) 
  ).'  
       
.-.-.  
'. % ) 
  ).'  
       
.-.-.  
'. & ) 
  ).'  
       
.-.-.  
'. ' ) 
  ).'  
       
.-.-.  
'. ( ) 
  ).'  
       
.-.-.  
'. ) ) 
  ).'  
       
.-.-.  
'. * ) 
  ).'  
       
.-.-.  
'. + ) 
  ).'  
       
.-.-.  
'. , ) 
  ).'  
       
.-.-.  
'. - ) 
  ).'  
       
.-.-.  
'. . ) 
  ).'  
       
.-.-.  
'. / ) 
  ).'  
       
.-.-.  
'. 0 ) 
  ).'  
       
.-.-.  
'. 1 ) 
  ).'  
       
.-.-.  
'. 2 ) 
  ).'  
       
.-.-.  
'. 3 ) 
  ).'  
       
.-.-.  
'. 4 ) 
  ).'  
       
.-.-.  
'. 5 ) 
  ).'  
       
.-.-.  
'. 6 ) 
  ).'  
       
.-.-.  
'.7  ) 
  ).'  
       
.-.-.  
'. 8 ) 
  ).'  
       
.-.-.  
'. 9 ) 
  ).'  
       
.-.-.  
'. : ) 
  ).'  
       
.-.-.  
'. ; ) 
  ).'  
       
.-.-.  
'. < ) 
  ).'  
       
.-.-.  
'. = ) 
  ).'  
       
.-.-.  
'. > ) 
  ).'  
       
.-.-.  
'. ? ) 
  ).'  
       
.-.-.  
'. @ ) 
  ).'  
       
.-.-.  
'. A ) 
  ).'  
       
.-.-.  
'. B ) 
  ).'  
       
.-.-.  
'. C ) 
  ).'  
       
.-.-.  
'. D ) 
  ).'  
       
.-.-.  
'. E ) 
  ).'  
       
.-.-.  
'. F ) 
  ).'  
       
.-.-.  
'. G ) 
  ).'  
       
.-.-.  
'. H ) 
  ).'  
       
.-.-.  
'. I ) 
  ).'  
       
.-.-.  
'. J ) 
  ).'  
       
.-.-.  
'. K ) 
  ).'  
       
.-.-.  
'. L ) 
  ).'  
       
.-.-.  
'. M ) 
  ).'  
       
.-.-.  
'. N ) 
  ).'  
       
.-.-.  
'. O ) 
  ).'  
       
.-.-.  
'. P ) 
  ).'  
       
.-.-.  
'. Q ) 
  ).'  
       
.-.-.  
'. R ) 
  ).'  
       
.-.-.  
'. S ) 
  ).'  
       
.-.-.  
'. T ) 
  ).'  
       
.-.-.  
'. U ) 
  ).'  
       
.-.-.  
'. V ) 
  ).'  
       
.-.-.  
'. W ) 
  ).'  
       
.-.-.  
'. X ) 
  ).'  
       
.-.-.  
'. Y ) 
  ).'  
       
.-.-.  
'. Z ) 
  ).'  
       
.-.-.  
'. [ ) 
  ).'  
       
.-.-.  
'. \ ) 
  ).'  
       
.-.-.  
'. ] ) 
  ).'  
       
.-.-.  
'. ^ ) 
  ).'  
       
.-.-.  
'. _ ) 
  ).'  
       
.-.-.  
'. ` ) 
  ).'  
       
.-.-.  
'. a ) 
  ).'  
       
.-.-.  
'. b ) 
  ).'  
       
.-.-.  
'. c ) 
  ).'  
       
.-.-.  
'. d ) 
  ).'  
       
.-.-.  
'. e ) 
  ).'  
       
.-.-.  
'. f ) 
  ).'  
       
.-.-.  
'. g ) 
  ).'  
       
.-.-.  
'. h ) 
  ).'  
       
.-.-.  
'. i ) 
  ).'  
       
.-.-.  
'. j ) 
  ).'  
       
.-.-.  
'. k ) 
  ).'  
       
.-.-.  
'. l ) 
  ).'  
       
.-.-.  
'. m ) 
  ).'  
       
.-.-.  
'. n ) 
  ).'  
       
.-.-.  
'. o ) 
  ).'  
       
.-.-.  
'. p ) 
  ).'  
       
.-.-.  
'. q ) 
  ).'  
       
.-.-.  
'. r ) 
  ).'  
       
.-.-.  
'. s ) 
  ).'  
       
.-.-.  
'. t ) 
  ).'  
       
.-.-.  
'. u ) 
  ).'  
       
.-.-.  
'. v ) 
  ).'  
       
.-.-.  
'. w ) 
  ).'  
       
.-.-.  
'. x ) 
  ).'  
       
.-.-.  
'. y ) 
  ).'  
       
.-.-.  
'. z ) 
  ).'  
       
.-.-.  
'. { ) 
  ).'  
       
.-.-.  
'. | ) 
  ).'  
       
.-.-.  
'. } ) 
  ).'  
       
.-.-.  
'. ~ ) 
  ).'  
       
.-.-.  
'. A ) 
  ).'  
       
.-.-.  
'. O ) 
  ).'  
       
.-.-.  
'. U ) 
  ).'  
       
.-.-.  
'. a ) 
  ).'  
       
.-.-.  
'. o ) 
  ).'  
       
.-.-.  
'. u ) 
  ).'  
       
      
.-.-. 
'._.' 
      


