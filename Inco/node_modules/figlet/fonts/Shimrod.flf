flf2a$ 6 5 13 15 13
HHscript by <PERSON><PERSON><PERSON> (<PERSON>), based on his own .sig
Figlet release 2.0 -- August 5, 1993

Explanation of first line:
flf2 - "magic number" for file identification
a    - should always be `a', for now
$    - the "hardblank" -- prints as a blank, but can't be smushed
6    - height of a character
5    - height of a character, not including descenders
15   - max line length (excluding comment lines) + a fudge factor
15   - default smushmode for this font (like "-m 15" on command line)
13   - number of comment lines

$$@
$$@
$$@
$$@
$$@
$$@@
.$@
|$@
|$@
 $@
o$@
 $@@
p q$@
   $@
   $@
   $@
   $@
   $@@
 | | $@
-+-+-$@
 | | $@
-+-+-$@
 | | $@
     $@@
 ,+. $@
( | `$@
 `+. $@
. | )$@
 `+' $@
     $@@
 _  ,$@
(_)/ $@
  /_ $@
 /(_)$@
'    $@
     $@@
 ,-.  $@
(   ) $@
 ;-: ,$@
(   X $@
 `-' `$@
      $@@
p$@
 $@
 $@
 $@
 $@
 $@@
 ,$@
/ $@
| $@
\ $@
 `$@
  $@@
. $@
 \$@
 |$@
 /$@
' $@
  $@@
  .  $@
`.|,'$@
 / \ $@
     $@
     $@
     $@@
   $@
 | $@
-+-$@
 | $@
   $@
   $@@
 $@
 $@
 $@
 $@
p$@
 $@@
   $@
   $@
---$@
   $@
   $@
   $@@
 $@
 $@
 $@
 $@
o$@
 $@@
    /$@
   / $@
  /  $@
 /   $@
/    $@
     $@@
 ,-. $@
/  /\$@
| / |$@
\/  /$@
 `-' $@
     $@@
 ,$@
'|$@
 |$@
 |$@
 '$@
  $@@
,-. $@
   )$@
  / $@
 /  $@
'--'$@
    $@@
,--,$@
  / $@
 `. $@
   )$@
`-' $@
    $@@
  ,.$@
 / |$@
'--|$@
   |$@
   '$@
    $@@
;--'$@
|   $@
`-. $@
   )$@
`-' $@
    $@@
 ,-. $@
/    $@
|,-. $@
(   )$@
 `-' $@
     $@@
,---,$@
   / $@
  /  $@
 /   $@
'    $@
     $@@
 ,-. $@
(   )$@
 ;-: $@
(   )$@
 `-' $@
     $@@
 ,-. $@
(   )$@
 `-'|$@
    /$@
 `-' $@
     $@@
 $@
o$@
 $@
o$@
 $@
 $@@
 $@
o$@
 $@
p$@
 $@
 $@@
   $@
 ,'$@
<  $@
 `.$@
   $@
   $@@
   $@
---$@
   $@
---$@
   $@
   $@@
   $@
`. $@
  >$@
,' $@
   $@
   $@@
 ,-. $@
(   )$@
  ,' $@
  '  $@
  o  $@
     $@@
 ,-. $@
/ ,-\$@
| | |$@
\ `-'$@
 `--'$@
     $@@
 ,. $@
/  \$@
|--|$@
|  |$@
'  '$@
    $@@
,-. $@
|  )$@
|-< $@
|  )$@
`-' $@
    $@@
 ,-.$@
/   $@
|   $@
\   $@
 `-'$@
    $@@
,-. $@
|  \$@
|  |$@
|  /$@
`-' $@
    $@@
,--.$@
|   $@
|-  $@
|   $@
`--'$@
    $@@
,--.$@
|   $@
|-  $@
|   $@
'   $@
    $@@
 ,-.$@
/   $@
| -.$@
\  |$@
 `-'$@
    $@@
.  .$@
|  |$@
|--|$@
|  |$@
'  '$@
    $@@
,$@
|$@
|$@
|$@
'$@
 $@@
 ,$@
 |$@
 |$@
 |$@
-'$@
  $@@
,  ,$@
| / $@
|<  $@
| \ $@
'  `$@
    $@@
,   $@
|   $@
|   $@
|   $@
`--'$@
    $@@
.   ,$@
|\ /|$@
| V |$@
|   |$@
'   '$@
     $@@
.  .$@
|\ |$@
| \|$@
|  |$@
'  '$@
    $@@
 ,-. $@
/   \$@
|   |$@
\   /$@
 `-' $@
     $@@
;-. $@
|  )$@
|-' $@
|   $@
'   $@
    $@@
 ,-.  $@
/   \ $@
|   | $@
\   X $@
 `-' `$@
      $@@
,-. $@
|  )$@
|-< $@
|  \$@
'  '$@
    $@@
 ,-. $@
(   `$@
 `-. $@
.   )$@
 `-' $@
     $@@
,---.$@
  |  $@
  |  $@
  |  $@
  '  $@
     $@@
.  .$@
|  |$@
|  |$@
|  |$@
`--`$@
    $@@
.   ,$@
|  / $@
| /  $@
|/   $@
'    $@
     $@@
,   .$@
| . |$@
| ) )$@
|/|/ $@
' '  $@
     $@@
.   ,$@
 \ / $@
  X  $@
 / \ $@
'   `$@
     $@@
.   ,$@
 \ / $@
  Y  $@
  |  $@
  '  $@
     $@@
,---,$@
   / $@
  /  $@
 /   $@
'---'$@
     $@@
,-$@
| $@
| $@
| $@
`-$@
  $@@
\    $@
 \   $@
  \  $@
   \ $@
    \$@
     $@@
-.$@
 |$@
 |$@
 |$@
-'$@
  $@@
 A $@
/ \$@
   $@
   $@
   $@
   $@@
   $@
   $@
   $@
   $@
   $@
---$@@
q$@
 $@
 $@
 $@
 $@
 $@@
   $@
   $@
,-:$@
| |$@
`-`$@
   $@@
.  $@
|  $@
|-.$@
| |$@
`-'$@
   $@@
   $@
   $@
,-.$@
|  $@
`-'$@
   $@@
  .$@
  |$@
,-|$@
| |$@
`-'$@
   $@@
   $@
   $@
,-.$@
|-'$@
`-'$@
   $@@
   $@
 ,-$@
 | $@
 |-$@
 | $@
-' $@@
   $@
   $@
,-:$@
| |$@
`-|$@
`-'$@@
.  $@
|  $@
|-.$@
| |$@
' '$@
   $@@
 $@
o$@
.$@
|$@
'$@
 $@@
  $@
 o$@
 ,$@
 |$@
 |$@
-'$@@
,  $@
|  $@
| ,$@
|< $@
' `$@
   $@@
.$@
|$@
|$@
|$@
'$@
 $@@
     $@
     $@
;-.-.$@
| | |$@
' ' '$@
     $@@
   $@
   $@
;-.$@
| |$@
' '$@
   $@@
   $@
   $@
,-.$@
| |$@
`-'$@
   $@@
   $@
   $@
;-.$@
| |$@
|-'$@
'  $@@
   $@
   $@
,-:$@
| |$@
`-|$@
  `$@@
   $@
   $@
;-.$@
|  $@
'  $@
   $@@
   $@
   $@
,-.$@
`-.$@
`-'$@
   $@@
.  $@
|  $@
|- $@
|  $@
`-'$@
   $@@
   $@
   $@
. .$@
| |$@
`-`$@
   $@@
   $@
   $@
. ,$@
|/ $@
'  $@
   $@@
     $@
     $@
, , ,$@
|/|/ $@
' '  $@
     $@@
   $@
   $@
. ,$@
 X $@
' `$@
   $@@
   $@
   $@
. .$@
| |$@
`-|$@
`-'$@@
   $@
   $@
,-,$@
 / $@
'-'$@
   $@@
,-$@
\ $@
< $@
/ $@
`-$@
  $@@
|$@
|$@
|$@
|$@
|$@
 $@@
-.$@
 /$@
 >$@
 \$@
-'$@
  $@@
   $@
'\,$@
   $@
   $@
   $@
   $@@
p,.q$@
/  \$@
|--|$@
|  |$@
'  '$@
    $@@
p,-.q$@
/   \$@
|   |$@
\   /$@
 `-' $@
     $@@
p  q$@
|  |$@
|  |$@
|  |$@
`--'$@
    $@@
   $@
p q$@
,-.$@
| |$@
`-'$@
   $@@
   $@
p q$@
,-.$@
| |$@
`-'$@
   $@@
,-. $@
|  )$@
| ( $@
|  )$@
|-' $@
|   $@@
$@
$@
$@
$@
$@
$@@
