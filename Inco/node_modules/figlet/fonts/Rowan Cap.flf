flf2a$ 6 5 18 4 21

Figlet conversion by <PERSON>, <EMAIL>, 1/1/95

rowancap.flf

From <EMAIL>
Date: Sat, 11 Dec 1993 12:59:18 -0600
From: <PERSON> <<EMAIL>>
Reply to: "Discussions about the multi-font large-letter program, FIGLET."
     <<EMAIL>>
To: Multiple recipients of list FIGLET-L <<EMAIL>>
Subject: Something for someone somewhere . ..

>From: Lockerpalooza <<EMAIL>>

Found the caps for a possible font for <PERSON>gle<PERSON>, if anyone would care to expand?

From: <EMAIL> (<PERSON>)
Subject:A Font.
Date: 10 Dec 1993 16:54:18 GMT

$  $@
$  $@
$  $@
$  $@
$  $@
$  $@@
@
@
@
@
@
@@
@
@
@
@
@
@@
@
@
@
@
@
@@
@
@
@
@
@
@@
@
@
@
@
@
@@
@
@
@
@
@
@@
@
@
@
@
@
@@
@
@
@
@
@
@@
@
@
@
@
@
@@
@
@
@
@
@
@@
@
@
@
@
@
@@
@
@
@
@
@
@@
@
@
@
@
@
@@
      @
      @
      @
$ amr$@
$dMP $@
      @@
@
@
@
@
@
@@
@
@
@
@
@
@@
@
@
@
@
@
@@
@
@
@
@
@
@@
@
@
@
@
@
@@
@
@
@
@
@
@@
@
@
@
@
@
@@
@
@
@
@
@
@@
@
@
@
@
@
@@
@
@
@
@
@
@@
@
@
@
@
@
@@
@
@
@
@
@
@@
@
@
@
@
@
@@
@
@
@
@
@
@@
@
@
@
@
@
@@
@
@
@
@
@
@@
@
@
@
@
@
@@
@
@
@
@
@
@@
    .aMMMb$@
   dMP"dMP$@
  dMMMMMP  @
 dMP dMP   @
dMP dMP    @
           @@
    dMMMMb$@
   dMP"dMP$@
  dMMMMK"  @
 dMP.aMF   @
dMMMMP"    @
           @@
   .aMMMb$@
  dMP"VMP$@
 dMP      @
dMP.aMP   @
VMMMP"    @
          @@
    dMMMMb$@
   dMP VMP$@
  dMP dMP  @
 dMP.aMP   @
dMMMMP"    @
           @@
    dMMMMMP$@
   dMP      @
  dMMMP     @
 dMP        @
dMMMMMP     @
            @@
    dMMMMMP$@
   dMP      @
  dMMMP     @
 dMP        @
dMP         @
            @@
   .aMMMMP$@
  dMP"     @
 dMP MMP"  @
dMP.dMP    @
VMMMP"     @
           @@
    dMP dMP$@
   dMP dMP  @
  dMMMMMP   @
 dMP dMP    @
dMP dMP     @
            @@
    dMP$@
   amr  @
  dMP   @
 dMP    @
dMP     @
        @@
   dMMMMMP$@
      dMP  @
     dMP   @
dK .dMP    @
VMMMP"     @
           @@
    dMP dMP$@
   dMP.dMP  @
  dMMMMK"   @
 dMP"AMF    @
dMP dMP     @
            @@
    dMP$@
   dMP  @
  dMP   @
 dMP    @
dMMMMMP$@
        @@
    dMMMMMMMMb$@
   dMP"dMP"dMP$@
  dMP dMP dMP  @
 dMP dMP dMP   @
dMP dMP dMP    @
               @@
    dMMMMb$@
   dMP dMP$@
  dMP dMP  @
 dMP dMP   @
dMP dMP    @
           @@
   .aMMMb$@
  dMP"dMP$@
 dMP dMP  @
dMP.aMP   @
VMMMP"    @
          @@
    dMMMMb$@
   dMP.dMP$@
  dMMMMP"  @
 dMP       @
dMP        @
           @@
   .aMMMb$@
  dMP"dMP$@
 dMP.dMP  @
dMP.MMP   @
VMMP"MP   @
          @@
    dMMMMb$@
   dMP.dMP$@
  dMMMMK"  @
 dMP"AMF   @
dMP dMP    @
           @@
   .dMMMb$@
  dMP" VP$@
  VMMMb   @
dP .dMP   @
VMMMP"    @
          @@
 dMMMMMMP$@
   dMP    @
  dMP     @
 dMP      @
dMP       @
          @@
   dMP dMP$@
  dMP dMP  @
 dMP dMP   @
dMP.aMP    @
VMMMP"     @
           @@
  dMP dMP$@
 dMP dMP  @
dMP dMP   @
YMvAP"    @
 VP"      @
          @@
   dMP dMP dMP$@
  dMP dMP dMP  @
 dMP dMP dMP   @
dMP.dMP.dMP    @
VMMMPVMMP"     @
               @@
    dMP dMP$@
   dMK.dMP  @
  .dMMMK"   @
 dMP"AMF    @
dMP dMP     @
            @@
   dMP dMP$@
  dMP.dMP  @
  VMMMMP   @
dA .dMP    @
VMMMP"     @
           @@
    dMMMMMP$@
     .dMP"  @
   .dMP"    @
 .dMP"      @
dMMMMMP     @
            @@
@
@
@
@
@
@@
@
@
@
@
@
@@
@
@
@
@
@
@@
@
@
@
@
@
@@
@
@
@
@
@
@@
@
@
@
@
@
@@
    .aMMMb$@
   dMP"dMP$@
  dMMMMMP  @
 dMP dMP   @
dMP dMP    @
           @@
    dMMMMb$@
   dMP"dMP$@
  dMMMMK"  @
 dMP.aMF   @
dMMMMP"    @
           @@
   .aMMMb$@
  dMP"VMP$@
 dMP      @
dMP.aMP   @
VMMMP"    @
          @@
    dMMMMb$@
   dMP VMP$@
  dMP dMP  @
 dMP.aMP   @
dMMMMP"    @
           @@
    dMMMMMP$@
   dMP      @
  dMMMP     @
 dMP        @
dMMMMMP     @
            @@
    dMMMMMP$@
   dMP      @
  dMMMP     @
 dMP        @
dMP         @
            @@
   .aMMMMP$@
  dMP"     @
 dMP MMP"  @
dMP.dMP    @
VMMMP"     @
           @@
    dMP dMP$@
   dMP dMP  @
  dMMMMMP   @
 dMP dMP    @
dMP dMP     @
            @@
    dMP$@
   amr  @
  dMP   @
 dMP    @
dMP     @
        @@
   dMMMMMP$@
      dMP  @
     dMP   @
dK .dMP    @
VMMMP"     @
           @@
    dMP dMP$@
   dMP.dMP  @
  dMMMMK"   @
 dMP"AMF    @
dMP dMP     @
            @@
    dMP$@
   dMP  @
  dMP   @
 dMP    @
dMMMMMP$@
        @@
    dMMMMMMMMb$@
   dMP"dMP"dMP @
  dMP dMP dMP  @
 dMP dMP dMP   @
dMP dMP dMP    @
               @@
    dMMMMb$@
   dMP dMP$@
  dMP dMP  @
 dMP dMP   @
dMP dMP    @
           @@
   .aMMMb$@
  dMP"dMP$@
 dMP dMP  @
dMP.aMP   @
VMMMP"    @
          @@
    dMMMMb$@
   dMP.dMP$@
  dMMMMP"  @
 dMP       @
dMP        @
           @@
   .aMMMb$@
  dMP"dMP$@
 dMP.dMP  @
dMP.MMP   @
VMMP"MP   @
          @@
    dMMMMb$@
   dMP.dMP$@
  dMMMMK"  @
 dMP"AMF   @
dMP dMP    @
           @@
   .dMMMb$@
  dMP" VP$@
  VMMMb   @
dP .dMP   @
VMMMP"    @
          @@
 dMMMMMMP$@
   dMP    @
  dMP     @
 dMP      @
dMP       @
          @@
   dMP dMP$@
  dMP dMP  @
 dMP dMP   @
dMP.aMP    @
VMMMP"     @
           @@
  dMP dMP$@
 dMP dMP  @
dMP dMP   @
YMvAP"    @
 VP"      @
          @@
   dMP dMP dMP$@
  dMP dMP dMP  @
 dMP dMP dMP   @
dMP.dMP.dMP    @
VMMMPVMMP"     @
               @@
    dMP dMP$@
   dMK.dMP  @
  .dMMMK"   @
 dMP"AMF    @
dMP dMP     @
            @@
   dMP dMP$@
  dMP.dMP  @
  VMMMMP   @
dA .dMP    @
VMMMP"     @
           @@
    dMMMMMP$@
     .dMP"  @
   .dMP"    @
 .dMP"      @
dMMMMMP     @
            @@
@
@
@
@
@
@@
@
@
@
@
@
@@
@
@
@
@
@
@@
@
@
@
@
@
@@
@
@
@
@
@
@@
@
@
@
@
@
@@
@
@
@
@
@
@@
@
@
@
@
@
@@
@
@
@
@
@
@@
@
@
@
@
@
@@
@
@
@
@
@
@@
