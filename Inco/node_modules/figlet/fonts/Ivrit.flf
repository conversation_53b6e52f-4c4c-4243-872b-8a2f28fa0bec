flf2a$ 6 5 76 15 14 1 16271
Ivrit (Hebrew) Unicode font assembled by <PERSON> <<EMAIL>>
Latin chars from  Standard by <PERSON><PERSON> & <PERSON>
Hebrew chars from Jerusalem by <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>
        Use "ilhebrew.flc" for Hebrew keyboard mapping
        Use "ushebrew.flc" for U.S.-style keyboard mapping ("febrew" script)
        Use "8859-8.flc" for ISO 8859-8 text
        Or use UTF-8
WARNING! FIGfonts aren't bidirectional; this is strictly right-to-left 
        (by default) even for the Latin characters.
figlet release 2.2 -- November 1996

Modified by <PERSON> <<EMAIL>> 12/96 to include new parameter
supported by FIGlet and FIGWin.  May also be slightly modified for better use
of new full-width/kern/smush alternatives, but default output is NOT changed.
 $@
 $@
 $@
 $@
 $@
 $@@
  _ @
 | |@
 | |@
 |_|@
 (_)@
    @@
  _ _ @
 ( | )@
  V V @
   $  @
   $  @
      @@
    _  _   @
  _| || |_ @
 |_  ..  _|@
 |_      _|@
   |_||_|  @
           @@
   _  @
  | | @
 / __)@
 \__ \@
 (   /@
  |_| @@
  _  __@
 (_)/ /@
   / / @
  / /_ @
 /_/(_)@
       @@
   ___   @
  ( _ )  @
  / _ \/\@
 | (_>  <@
  \___/\/@
         @@
  _ @
 ( )@
 |/ @
  $ @
  $ @
    @@
   __@
  / /@
 | | @
 | | @
 | | @
  \_\@@
 __  @
 \ \ @
  | |@
  | |@
  | |@
 /_/ @@
       @
 __/\__@
 \    /@
 /_  _\@
   \/  @
       @@
        @
    _   @
  _| |_ @
 |_   _|@
   |_|  @
        @@
    @
    @
    @
  _ @
 ( )@
 |/ @@
        @
        @
  _____ @
 |_____|@
    $   @
        @@
    @
    @
    @
  _ @
 (_)@
    @@
     __@
    / /@
   / / @
  / /  @
 /_/   @
       @@
   ___  @
  / _ \ @
 | | | |@
 | |_| |@
  \___/ @
        @@
  _ @
 / |@
 | |@
 | |@
 |_|@
    @@
  ____  @
 |___ \ @
   __) |@
  / __/ @
 |_____|@
        @@
  _____ @
 |___ / @
   |_ \ @
  ___) |@
 |____/ @
        @@
  _  _   @
 | || |  @
 | || |_ @
 |__   _|@
    |_|  @
         @@
  ____  @
 | ___| @
 |___ \ @
  ___) |@
 |____/ @
        @@
   __   @
  / /_  @
 | '_ \ @
 | (_) |@
  \___/ @
        @@
  _____ @
 |___  |@
    / / @
   / /  @
  /_/   @
        @@
   ___  @
  ( _ ) @
  / _ \ @
 | (_) |@
  \___/ @
        @@
   ___  @
  / _ \ @
 | (_) |@
  \__, |@
    /_/ @
        @@
    @
  _ @
 (_)@
  _ @
 (_)@
    @@
    @
  _ @
 (_)@
  _ @
 ( )@
 |/ @@
   __@
  / /@
 / / @
 \ \ @
  \_\@
     @@
        @
  _____ @
 |_____|@
 |_____|@
    $   @
        @@
 __  @
 \ \ @
  \ \@
  / /@
 /_/ @
     @@
  ___ @
 |__ \@
   / /@
  |_| @
  (_) @
      @@
    ____  @
   / __ \ @
  / / _` |@
 | | (_| |@
  \ \__,_|@
   \____/ @@
     _    @
    / \   @
   / _ \  @
  / ___ \ @
 /_/   \_\@
          @@
  ____  @
 | __ ) @
 |  _ \ @
 | |_) |@
 |____/ @
        @@
   ____ @
  / ___|@
 | |    @
 | |___ @
  \____|@
        @@
  ____  @
 |  _ \ @
 | | | |@
 | |_| |@
 |____/ @
        @@
  _____ @
 | ____|@
 |  _|  @
 | |___ @
 |_____|@
        @@
  _____ @
 |  ___|@
 | |_   @
 |  _|  @
 |_|    @
        @@
   ____ @
  / ___|@
 | |  _ @
 | |_| |@
  \____|@
        @@
  _   _ @
 | | | |@
 | |_| |@
 |  _  |@
 |_| |_|@
        @@
  ___ @
 |_ _|@
  | | @
  | | @
 |___|@
      @@
      _ @
     | |@
  _  | |@
 | |_| |@
  \___/ @
        @@
  _  __@
 | |/ /@
 | ' / @
 | . \ @
 |_|\_\@
       @@
  _     @
 | |    @
 | |    @
 | |___ @
 |_____|@
        @@
  __  __ @
 |  \/  |@
 | |\/| |@
 | |  | |@
 |_|  |_|@
         @@
  _   _ @
 | \ | |@
 |  \| |@
 | |\  |@
 |_| \_|@
        @@
   ___  @
  / _ \ @
 | | | |@
 | |_| |@
  \___/ @
        @@
  ____  @
 |  _ \ @
 | |_) |@
 |  __/ @
 |_|    @
        @@
   ___  @
  / _ \ @
 | | | |@
 | |_| |@
  \__\_\@
        @@
  ____  @
 |  _ \ @
 | |_) |@
 |  _ < @
 |_| \_\@
        @@
  ____  @
 / ___| @
 \___ \ @
  ___) |@
 |____/ @
        @@
  _____ @
 |_   _|@
   | |  @
   | |  @
   |_|  @
        @@
  _   _ @
 | | | |@
 | | | |@
 | |_| |@
  \___/ @
        @@
 __     __@
 \ \   / /@
  \ \ / / @
   \ V /  @
    \_/   @
          @@
 __        __@
 \ \      / /@
  \ \ /\ / / @
   \ V  V /  @
    \_/\_/   @
             @@
 __  __@
 \ \/ /@
  \  / @
  /  \ @
 /_/\_\@
       @@
 __   __@
 \ \ / /@
  \ V / @
   | |  @
   |_|  @
        @@
  _____@
 |__  /@
   / / @
  / /_ @
 /____|@
       @@
  __ @
 | _|@
 | | @
 | | @
 | | @
 |__|@@
 __    @
 \ \   @
  \ \  @
   \ \ @
    \_\@
       @@
  __ @
 |_ |@
  | |@
  | |@
  | |@
 |__|@@
  /\ @
 |/\|@
   $ @
   $ @
   $ @
     @@
        @
        @
        @
        @
  _____ @
 |_____|@@
  _ @
 ( )@
  \|@
  $ @
  $ @
    @@
        @
   __ _ @
  / _` |@
 | (_| |@
  \__,_|@
        @@
  _     @
 | |__  @
 | '_ \ @
 | |_) |@
 |_.__/ @
        @@
       @
   ___ @
  / __|@
 | (__ @
  \___|@
       @@
      _ @
   __| |@
  / _` |@
 | (_| |@
  \__,_|@
        @@
       @
   ___ @
  / _ \@
 |  __/@
  \___|@
       @@
   __ @
  / _|@
 | |_ @
 |  _|@
 |_|  @
      @@
        @
   __ _ @
  / _` |@
 | (_| |@
  \__, |@
  |___/ @@
  _     @
 | |__  @
 | '_ \ @
 | | | |@
 |_| |_|@
        @@
  _ @
 (_)@
 | |@
 | |@
 |_|@
    @@
    _ @
   (_)@
   | |@
   | |@
  _/ |@
 |__/ @@
  _    @
 | | __@
 | |/ /@
 |   < @
 |_|\_\@
       @@
  _ @
 | |@
 | |@
 | |@
 |_|@
    @@
            @
  _ __ ___  @
 | '_ ` _ \ @
 | | | | | |@
 |_| |_| |_|@
            @@
        @
  _ __  @
 | '_ \ @
 | | | |@
 |_| |_|@
        @@
        @
   ___  @
  / _ \ @
 | (_) |@
  \___/ @
        @@
        @
  _ __  @
 | '_ \ @
 | |_) |@
 | .__/ @
 |_|    @@
        @
   __ _ @
  / _` |@
 | (_| |@
  \__, |@
     |_|@@
       @
  _ __ @
 | '__|@
 | |   @
 |_|   @
       @@
      @
  ___ @
 / __|@
 \__ \@
 |___/@
      @@
  _   @
 | |_ @
 | __|@
 | |_ @
  \__|@
      @@
        @
  _   _ @
 | | | |@
 | |_| |@
  \__,_|@
        @@
        @
 __   __@
 \ \ / /@
  \ V / @
   \_/  @
        @@
           @
 __      __@
 \ \ /\ / /@
  \ V  V / @
   \_/\_/  @
           @@
       @
 __  __@
 \ \/ /@
  >  < @
 /_/\_\@
       @@
        @
  _   _ @
 | | | |@
 | |_| |@
  \__, |@
  |___/ @@
      @
  ____@
 |_  /@
  / / @
 /___|@
      @@
    __@
   / /@
  | | @
 < <  @
  | | @
   \_\@@
  _ @
 | |@
 | |@
 | |@
 | |@
 |_|@@
 __   @
 \ \  @
  | | @
   > >@
  | | @
 /_/  @@
  /\/|@
 |/\/ @
   $  @
   $  @
   $  @
      @@
  _   _ @
 (_)_(_)@
   /_\  @
  / _ \ @
 /_/ \_\@
        @@
  _   _ @
 (_)_(_)@
  / _ \ @
 | |_| |@
  \___/ @
        @@
  _   _ @
 (_) (_)@
 | | | |@
 | |_| |@
  \___/ @
        @@
  _   _ @
 (_)_(_)@
  / _` |@
 | (_| |@
  \__,_|@
        @@
  _   _ @
 (_)_(_)@
  / _ \ @
 | (_) |@
  \___/ @
        @@
  _   _ @
 (_) (_)@
 | | | |@
 | |_| |@
  \__,_|@
        @@
   ___ @
  / _ \@
 | |/ /@
 | |\ \@
 | ||_/@
 |_|   @@
160  NO-BREAK SPACE
 $@
 $@
 $@
 $@
 $@
 $@@
173  SOFT HYPHEN
        @
        @
  _____ @
 |_____|@
    $   @
        @@
196  LATIN CAPITAL LETTER A WITH DIAERESIS
  _   _ @
 (_)_(_)@
   /_\  @
  / _ \ @
 /_/ \_\@
        @@
214  LATIN CAPITAL LETTER O WITH DIAERESIS
  _   _ @
 (_)_(_)@
  / _ \ @
 | |_| |@
  \___/ @
        @@
220  LATIN CAPITAL LETTER U WITH DIAERESIS
  _   _ @
 (_) (_)@
 | | | |@
 | |_| |@
  \___/ @
        @@
223  LATIN SMALL LETTER SHARP S
   ___ @
  / _ \@
 | |/ /@
 | |\ \@
 | ||_/@
 |_|   @@
228  LATIN SMALL LETTER A WITH DIAERESIS
  _   _ @
 (_)_(_)@
  / _` |@
 | (_| |@
  \__,_|@
        @@
246  LATIN SMALL LETTER O WITH DIAERESIS
  _   _ @
 (_)_(_)@
  / _ \ @
 | (_) |@
  \___/ @
        @@
252  LATIN SMALL LETTER U WITH DIAERESIS
  _   _ @
 (_) (_)@
 | | | |@
 | |_| |@
  \__,_|@
        @@
0x05D0  HEBREW LETTER ALEF
 __   __@
 \ \ / /@
 |  V / @
 | |\ \ @
 |_| \_\@
        @@
0x05D1  HEBREW LETTER BET
   ______  @
  |____  | @
       | | @
  _____| |_@
 /________/@
           @@
0x05D2  HEBREW LETTER GIMEL
    ____  @
   |__  | @
      | | @
  ____| | @
 /____/\_\@
          @@
0x05D3  HEBREW LETTER DALET
  _______ @
 |____   |@
      | | @
      | | @
      |_| @
          @@
0x05D4  HEBREW LETTER HE
  _______ @
 |_____  |@
   _   | |@
  | |  | |@
  |_|  |_|@
          @@
0x05D5  HEBREW LETTER VAV
  ___ @
 |_  |@
   | |@
   | |@
   |_|@
      @@
0x05D6  HEBREW LETTER ZAYIN
 ________ @
 \__   __\@
    | |   @
    | |   @
    |_|   @
          @@
0x05D7  HEBREW LETTER HET
  _______ @
 |.  __  |@
  | |  | |@
  | |  | |@
  |_|  |_|@
          @@
0x05D8  HEBREW LETTER TET
  __   ___ @
 |. | /_  |@
  | |   | |@
  | |___| |@
  |_______|@
           @@
0x05D9  HEBREW LETTER YOD
  ___ @
 |_  |@
   |_|@
    $ @
    $ @
      @@
0x05DA  HEBREW LETTER FINAL KAF
  _______ @
 |____  .|@
      | | @
      | | @
      | | @
      |_| @@
0x05DB  HEBREW LETTER KAF
  _____  @
 |____ \ @
      | |@
  ____| |@
 |_____/ @
         @@
0x05DC  HEBREW LETTER LAMED
 |=|____ @
 |____  |@
     / / @
    / /  @
   /_/   @
         @@
0x05DD  HEBREW LETTER FINAL MEM
  ________ @
 |.  ___  |@
  | |   | |@
  | |___| |@
  |_______|@
           @@
0x05DE  HEBREW LETTER MEM
  _______ @
 |.  __  |@
  | |  | |@
  | | _| |@
  |_||___|@
          @@
0x05DF  HEBREW LETTER FINAL NUN
  ___ @
 |_  |@
   | |@
   | |@
   | |@
   |_|@@
0x05E0  HEBREW LETTER NUN
   ___ @
  |_  |@
    | |@
  __| |@
 |____|@
       @@
0x05E1  HEBREW LETTER SAMEKH
  _______ @
 |.  __  |@
  | |  | |@
  | |__/ |@
  |_____/ @
          @@
0x05E2  HEBREW LETTER AYIN
  __   _ @
  \ \ | |@
   \ \| |@
  __\ ` |@
 |______|@
         @@
0x05E3  HEBREW LETTER FINAL PE
  ______ @
 |  __  |@
 | |_ | |@
 |___|| |@
      | |@
      |_|@@
0x05E4  HEBREW LETTER PE
  _______ @
 |  ___  |@
  \_\  | |@
  _____| |@
 |_______|@
          @@
0x05E5  HEBREW LETTER FINAL TSADI
  __   _ @
 |. | | |@
  | | // @
  | |//  @
  | |    @
  |_|    @@
0x05E6  HEBREW LETTER TSADI
   __   __.@
   \ \ / / @
    \ V /  @
  ___\  \  @
 |______|  @
           @@
0x05E7  HEBREW LETTER QOF
  ______ @
 |____  |@
   _  | |@
  | | |_|@
  | |    @
  |_|    @@
0x05E8  HEBREW LETTER RESH
  ______ @
 |____  |@
      | |@
      | |@
      |_|@
         @@
0x05E9  HEBREW LETTER SHIN
  _   _   _ @
 | | | | | |@
 | | | | | |@
 | |/ /_/ / @
 |_______/  @
            @@
0x05EA  HEBREW LETTER TAV
    ______ @
   |  __  |@
   | |  | |@
  _| |  | |@
 |___|  |_|@
           @@
0x2721  STAR OF DAVID
       @
 __/\__@
 \    /@
 /_  _\@
   \/  @
       @@
-0x0002  
aleph = t, bet/vet = c, gimel = d, dalet = s, he = v, vav = u, zayin = z  @
het = j, tet = y, yod = h, kaf/chaf = f, final kaf = l, lamed = k, mem = n@
final mem = o, nun = b, final nun = i, samekh = x, ayin = g, pe/fe = p,   @
final pe = ;, tsadi = m, final tsadi = ., qof = e, resh = r, shin/sin = a @
tav = , comma = ', period = /, semicolon = `, slash = q, apostrophe = w   @
Star of David = *                                                         @@
-0x0003  
aleph = a, bet/vet = b, gimel = g, dalet = d, he = h, vav = v, zayin = z  @
het = c, tet = t, yod = y, kaf/chaf = k, final kaf = f, lamed = l, mem = m@
final mem = o, nun = n, final nun = i, samekh = e, ayin = _, pe/fe = p,   @
final pe = u, tsadi = j, final tsadi = w, qof = q, resh = r, shin/sin = s @
tav = x                                                                   @
Star of David = *                                                         @@
