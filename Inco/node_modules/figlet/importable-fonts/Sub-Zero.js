export default `flf2a$ 6 5 17 -1 16

                  "Sub-Zero" font by Sub-Zero
                 ==============================


-> Conversion to FigLet font by MEPH. (Part of ASCII Editor Service Pack I)
   (http://studenten.freepage.de/meph/ascii/ascii/editor/_index.htm)
-> Defined: ASCII code alphabet
-> Uppercase characters only.

ScarecrowsASCIIArtArchive1.0.txt
From: "Sub-Zero" <<EMAIL>>
"Here's a font I've been working on lately.  Can someone make the V, Q, and X
look better?  Also, the B, P, and R could use an improvement too.
Oh, here it is."

$$$@
$$$@
$$$@
$$$@
$$$@
$$$@@
@
@
@
@
@
@@
@
@
@
@
@
@@
@
@
@
@
@
@@
@
@
@
@
@
@@
@
@
@
@
@
@@
@
@
@
@
@
@@
@
@
@
@
@
@@
@
@
@
@
@
@@
@
@
@
@
@
@@
@
@
@
@
@
@@
@
@
@
@
@
@@
@
@
@
@
@
@@
@
@
@
@
@
@@
@
@
@
@
@
@@
@
@
@
@
@
@@
@
@
@
@
@
@@
@
@
@
@
@
@@
@
@
@
@
@
@@
@
@
@
@
@
@@
@
@
@
@
@
@@
@
@
@
@
@
@@
@
@
@
@
@
@@
@
@
@
@
@
@@
@
@
@
@
@
@@
@
@
@
@
@
@@
@
@
@
@
@
@@
@
@
@
@
@
@@
@
@
@
@
@
@@
@
@
@
@
@
@@
@
@
@
@
@
@@
@
@
@
@
@
@@
@
@
@
@
@
@@
 ______    @
/\\  __ \\   @
\\ \\  __ \\  @
 \\ \\_\\ \\_\\ @
  \\/_/\\/_/ @
           @@
 ______    @
/\\  == \\   @
\\ \\  __<   @
 \\ \\_____\\ @
  \\/_____/ @
           @@
 ______    @
/\\  ___\\   @
\\ \\ \\____  @
 \\ \\_____\\ @
  \\/_____/ @
           @@
 _____    @
/\\  __-.  @
\\ \\ \\/\\ \\ @
 \\ \\____- @
  \\/____/ @
          @@
 ______    @
/\\  ___\\   @
\\ \\  __\\   @
 \\ \\_____\\ @
  \\/_____/ @
           @@
 ______  @
/\\  ___\\ @
\\ \\  __\\ @
 \\ \\_\\   @
  \\/_/   @
         @@
 ______    @
/\\  ___\\   @
\\ \\ \\__ \\  @
 \\ \\_____\\ @
  \\/_____/ @
           @@
 __  __    @
/\\ \\_\\ \\   @
\\ \\  __ \\  @
 \\ \\_\\ \\_\\ @
  \\/_/\\/_/ @
           @@
 __    @
/\\ \\   @
\\ \\ \\  @
 \\ \\_\\ @
  \\/_/ @
       @@
   __    @
  /\\ \\   @
 _\\_\\ \\  @
/\\_____\\ @
\\/_____/ @
         @@
 __  __    @
/\\ \\/ /    @
\\ \\  _"-.  @
 \\ \\_\\ \\_\\ @
  \\/_/\\/_/ @
           @@
 __        @
/\\ \\       @
\\ \\ \\____  @
 \\ \\_____\\ @
  \\/_____/ @
           @@
 __    __    @
/\\ "-./  \\   @
\\ \\ \\-./\\ \\  @
 \\ \\_\\ \\ \\_\\ @
  \\/_/  \\/_/ @
             @@
 __   __    @
/\\ "-.\\ \\   @
\\ \\ \\-.  \\  @
 \\ \\_\\\\"\\_\\ @
  \\/_/ \\/_/ @
            @@
 ______    @
/\\  __ \\   @
\\ \\ \\/\\ \\  @
 \\ \\_____\\ @
  \\/_____/ @
           @@
 ______  @
/\\  == \\ @
\\ \\  _-/ @
 \\ \\_\\   @
  \\/_/   @
         @@
 ______    @
/\\  __ \\   @
\\ \\ \\/\\_\\  @
 \\ \\___\\_\\ @
  \\/___/_/ @
           @@
 ______    @
/\\  == \\   @
\\ \\  __<   @
 \\ \\_\\ \\_\\ @
  \\/_/ /_/ @
           @@
 ______    @
/\\  ___\\   @
\\ \\___  \\  @
 \\/\\_____\\ @
  \\/_____/ @
           @@
 ______  @
/\\__  _\\ @
\\/_/\\ \\/ @
   \\ \\_\\ @
    \\/_/ @
         @@
 __  __    @
/\\ \\/\\ \\   @
\\ \\ \\_\\ \\  @
 \\ \\_____\\ @
  \\/_____/ @
           @@
 __   __  @
/\\ \\ / /  @
\\ \\ \\'/   @
 \\ \\__|   @
  \\/_/    @
          @@
 __     __    @
/\\ \\  _ \\ \\   @
\\ \\ \\/ ".\\ \\  @
 \\ \\__/".~\\_\\ @
  \\/_/   \\/_/ @
              @@
 __  __    @
/\\_\\_\\_\\   @
\\/_/\\_\\/_  @
  /\\_\\/\\_\\ @
  \\/_/\\/_/ @
           @@
 __  __    @
/\\ \\_\\ \\   @
\\ \\____ \\  @
 \\/\\_____\\ @
  \\/_____/ @
           @@
 ______    @
/\\___  \\   @
\\/_/  /__  @
  /\\_____\\ @
  \\/_____/ @
           @@
@
@
@
@
@
@@
@
@
@
@
@
@@
@
@
@
@
@
@@
@
@
@
@
@
@@
@
@
@
@
@
@@
@
@
@
@
@
@@
 ______    @
/\\  __ \\   @
\\ \\  __ \\  @
 \\ \\_\\ \\_\\ @
  \\/_/\\/_/ @
           @@
 ______    @
/\\  == \\   @
\\ \\  __<   @
 \\ \\_____\\ @
  \\/_____/ @
           @@
 ______    @
/\\  ___\\   @
\\ \\ \\____  @
 \\ \\_____\\ @
  \\/_____/ @
           @@
 _____    @
/\\  __-.  @
\\ \\ \\/\\ \\ @
 \\ \\____- @
  \\/____/ @
          @@
 ______    @
/\\  ___\\   @
\\ \\  __\\   @
 \\ \\_____\\ @
  \\/_____/ @
           @@
 ______  @
/\\  ___\\ @
\\ \\  __\\ @
 \\ \\_\\   @
  \\/_/   @
         @@
 ______    @
/\\  ___\\   @
\\ \\ \\__ \\  @
 \\ \\_____\\ @
  \\/_____/ @
           @@
 __  __    @
/\\ \\_\\ \\   @
\\ \\  __ \\  @
 \\ \\_\\ \\_\\ @
  \\/_/\\/_/ @
           @@
 __    @
/\\ \\   @
\\ \\ \\  @
 \\ \\_\\ @
  \\/_/ @
       @@
   __    @
  /\\ \\   @
 _\\_\\ \\  @
/\\_____\\ @
\\/_____/ @
         @@
 __  __    @
/\\ \\/ /    @
\\ \\  _"-.  @
 \\ \\_\\ \\_\\ @
  \\/_/\\/_/ @
           @@
 __        @
/\\ \\       @
\\ \\ \\____  @
 \\ \\_____\\ @
  \\/_____/ @
           @@
 __    __    @
/\\ "-./  \\   @
\\ \\ \\-./\\ \\  @
 \\ \\_\\ \\ \\_\\ @
  \\/_/  \\/_/ @
             @@
 __   __    @
/\\ "-.\\ \\   @
\\ \\ \\-.  \\  @
 \\ \\_\\\\"\\_\\ @
  \\/_/ \\/_/ @
            @@
 ______    @
/\\  __ \\   @
\\ \\ \\/\\ \\  @
 \\ \\_____\\ @
  \\/_____/ @
           @@
 ______  @
/\\  == \\ @
\\ \\  _-/ @
 \\ \\_\\   @
  \\/_/   @
         @@
 ______    @
/\\  __ \\   @
\\ \\ \\/\\_\\  @
 \\ \\___\\_\\ @
  \\/___/_/ @
           @@
 ______    @
/\\  == \\   @
\\ \\  __<   @
 \\ \\_\\ \\_\\ @
  \\/_/ /_/ @
           @@
 ______    @
/\\  ___\\   @
\\ \\___  \\  @
 \\/\\_____\\ @
  \\/_____/ @
           @@
 ______  @
/\\__  _\\ @
\\/_/\\ \\/ @
   \\ \\_\\ @
    \\/_/ @
         @@
 __  __    @
/\\ \\/\\ \\   @
\\ \\ \\_\\ \\  @
 \\ \\_____\\ @
  \\/_____/ @
           @@
 __   __  @
/\\ \\ / /  @
\\ \\ \\'/   @
 \\ \\__|   @
  \\/_/    @
          @@
 __     __    @
/\\ \\  _ \\ \\   @
\\ \\ \\/ ".\\ \\  @
 \\ \\__/".~\\_\\ @
  \\/_/   \\/_/ @
              @@
 __  __    @
/\\_\\_\\_\\   @
\\/_/\\_\\/_  @
  /\\_\\/\\_\\ @
  \\/_/\\/_/ @
           @@
 __  __    @
/\\ \\_\\ \\   @
\\ \\____ \\  @
 \\/\\_____\\ @
  \\/_____/ @
           @@
 ______    @
/\\___  \\   @
\\/_/  /__  @
  /\\_____\\ @
  \\/_____/ @
           @@
@
@
@
@
@
@@
@
@
@
@
@
@@
@
@
@
@
@
@@
@
@
@
@
@
@@
@
@
@
@
@
@@
@
@
@
@
@
@@
@
@
@
@
@
@@
@
@
@
@
@
@@
@
@
@
@
@
@@
@
@
@
@
@
@@
@
@
@
@
@
@@
`