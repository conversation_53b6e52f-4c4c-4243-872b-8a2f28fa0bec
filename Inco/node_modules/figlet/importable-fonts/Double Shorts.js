export default `flf2a$ 3 3 10 -1 34 0 7999 0
Author : myflix
Date   : 2004/2/16 15:18:10
Version: 1.0
-------------------------------------------------
This font is a variation of double designed by <PERSON> 11/24/94
double.flf (FIGlet font)

figlet conversion by <PERSON>, <EMAIL>, 11/24/94

From: <EMAIL> (<PERSON>)
Newsgroups: alt.ascii-art
Subject: FONT: double (not .flf)
Date: 15 Mar 1994 16:20:01 -0000
Organization: Commodore 64 Services, University of Warwick, UK
Lines: 30
Message-ID: <2m4n7h$<EMAIL>>
NNTP-Posting-Host: basil.csv.warwick.ac.uk
Mime-Version: 1.0
Content-Type: text/plain; charset=US-ASCII
Content-Transfer-Encoding: 7bit
Keywords: font
Status: RO

-------------------------------------------------
This font has been created using Jav<PERSON>'s FIGlet font export assistant.
Have a look at: http://www.jave.de

Permission is hereby given to modify this font, as long as the
modifier's name is placed on a comment line.

---

Font modified June 17, 2007 by patorjk 
This was to widen the space character.
$ $#
$ $#
$ $##
__ #
|| #
.. ##
   #
// #
   ##
 __ __  #
=||=||= #
 || ||  ##
$#
 #
 ##
   _  #
O //  #
 // O ##
&#
 #
 ##
// #
   #
   ##
 _  #
((  #
 \\\\ ##
 _  #
 )) #
//  ##
*#
 #
 ##
+#
 #
 ##
   #
   #
// ##
-#
 #
 ##
   #
   #
|| ##
/#
 #
 ##
0#
 #
 ##
1#
 #
 ##
2#
 #
 ##
3#
 #
 ##
4#
 #
 ##
5#
 #
 ##
6#
 #
 ##
7#
 #
 ##
8#
 #
 ##
9#
 #
 ##
:#
 #
 ##
;#
 #
 ##
<#
 #
 ##
=#
 #
 ##
>#
 #
 ##
____ #
'_// #
 ||  ##
@#
 #
 ##
 ___  #
||=|| #
|| || ##
____  #
||=)  #
||_)) ##
 ____ #
((    #
 \\\\__ ##
_____ #
||  ) #
||_// ##
_____ #
||==  #
||___ ##
_____ #
||==  #
||    ##
 ____  #
(( ___ #
 \\\\_|| ##
__  __ #
||==|| #
||  || ##
__ #
|| #
|| ##
   __ #
   || #
|__|| ##
__ __ #
||<<  #
|| \\\\ ##
__    #
||    #
||__| ##
___  __ #
|| \\/ | #
||    | ##
__  __ #
||\\\\|| #
|| \\|| ##
 _____  #
((   )) #
 \\\\_//  ##
_____ #
||_// #
||    ##
 _____  #
((   )) #
 \\\\_/X| ##
_____ #
||_// #
|| \\\\ ##
  __ #
 ((  #
\\_)) ##
_____ #
 ||   #
 ||   ##
__ __ #
|| || #
\\\\_// ##
__ __ #
\\\\ // #
 \\V/  ##
__    __#
\\\\ /\\ //#
 \\V/\\V/ ##
_  _ #
\\\\// #
//\\\\ ##
_  _ #
\\\\// #
 //  ##
____  #
  //  #
 //__ ##
[#
 #
 ##
\\#
 #
 ##
]#
 #
 ##
^#
 #
 ##
_#
 #
 ##
\`#
 #
 ##
 ___  #
||=|| #
|| || ##
____  #
||=)  #
||_)) ##
 ____ #
((    #
 \\\\__ ##
_____ #
||  ) #
||_// ##
_____ #
||==  #
||___ ##
_____ #
||==  #
||    ##
 ____  #
(( ___ #
 \\\\_|| ##
__  __ #
||==|| #
||  || ##
__ #
|| #
|| ##
   __ #
   || #
|__|| ##
__ __ #
||<<  #
|| \\\\ ##
__    #
||    #
||__| ##
___  __ #
|| \\/ | #
||    | ##
__  __ #
||\\\\|| #
|| \\|| ##
 _____  #
((   )) #
 \\\\_//  ##
_____ #
||_// #
||    ##
 _____  #
((   )) #
 \\\\_/X| ##
_____ #
||_// #
|| \\\\ ##
  __ #
 ((  #
\\_)) ##
_____ #
 ||   #
 ||   ##
__ __ #
|| || #
\\\\_// ##
__ __ #
\\\\ // #
 \\V/  ##
__    __#
\\\\ /\\ //#
 \\V/\\V/ ##
_  _ #
\\\\// #
//\\\\ ##
_  _ #
\\\\// #
 //  ##
____  #
  //  #
 //__ ##
{#
 #
 ##
|#
 #
 ##
}#
 #
 ##
~#
 #
 ##
 ___  #
||=|| #
|| || ##
 _____  #
((   )) #
 \\\\_//  ##
__ __ #
|| || #
\\\\_// ##
 ___  #
||=|| #
|| || ##
 _____  #
((   )) #
 \\\\_//  ##
__ __ #
|| || #
\\\\_// ##
   #
   #
 _ ##
`