export default `flf2a 1 1 10 -1 16
B1FF by <PERSON> 12/94 based on Terminal by <PERSON>
Includes characters 128-255
figlet release 2.1 -- 12 Aug 1994
Permission is hereby given to modify this font, as long as the
modifier's name is placed on a comment line.

Explanation of first line:
flf2 - "magic number" for file identification
a    - should always be \`a', for now
   - the "hardblank" -- prints as a blank, but can't be smushed
1    - height of a character
1    - height of a character, not including descenders
10   - max line length (excluding comment lines) + a fudge factor
-1   - default smushmode for this font
16   - number of comment lines

@
!@
"@
#@
$@
%@
&@
'@
(@
)@
*@
+@
,@
-@
.@
/@
0@
1@
2@
3@
4@
5@
6@
7@
8@
9@
:@
;@
<@
=@
>@
?@
@#
/-\\@
|3@
(@
|)@
3@
F@
6@
H@
1@
_/@
/<@
|_@
/\\/\\@
/\\/@
0@
P@
Q@
R@
5@
T@
|_|@
\\/@
\\/\\/@
X@
Y@
Z@
[@
\\@
]@
^@
_@
\`@
/-\\@
|3@
(@
|)@
3@
F@
6@
H@
1@
_/@
/<@
|_@
/\\/\\@
/\\/@
0@
P@
Q@
R@
5@
T@
|_|@
\\/@
\\/\\/@
X@
Y@
Z@
{@
|@
}@
~@
�@
�@
�@
�@
�@
�@
�@
128
�@
129
�@
130
�@
131
�@
132
�@
133
�@
134
�@
135
�@
136
�@
137
�@
138
�@
139
�@
140
�@
141
�@
142
�@
143
�@
144
�@
145
�@
146
�@
147
�@
148
�@
149
�@
150
�@
151
�@
152
�@
153
�@
154
�@
155
�@
156
�@
157
�@
158
�@
159
�@
160
�@
161
�@
162
�@
163
�@
164
�@
165
�@
166
�@
167
�@
168
�@
169
�@
170
�@
171
�@
172
�@
173
�@
174
�@
175
�@
176
�@
177
�@
178
�@
179
�@
180
�@
181
�@
182
�@
183
�@
184
�@
185
�@
186
�@
187
�@
188
�@
189
�@
190
�@
191
�@
192
�@
193
�@
194
�@
195
�@
196
�@
197
�@
198
�@
199
�@
200
�@
201
�@
202
�@
203
�@
204
�@
205
�@
206
�@
207
�@
208
�@
209
�@
210
�@
211
�@
212
�@
213
�@
214
�@
215
�@
216
�@
217
�@
218
�@
219
�@
220
�@
221
�@
222
�@
223
�@
224
�@
225
�@
226
�@
227
�@
228
�@
229
�@
230
�@
231
�@
232
�@
233
�@
234
�@
235
�@
236
�@
237
�@
238
�@
239
�@
240
�@
241
�@
242
�@
243
�@
244
�@
245
�@
246
�@
247
�@
248
�@
249
�@
250
�@
251
�@
252
�@
253
�@
254
�@
255
�@
`