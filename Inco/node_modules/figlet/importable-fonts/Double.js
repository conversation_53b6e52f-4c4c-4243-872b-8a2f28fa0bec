export default `flf2a$ 5 4 12 -1 21 0 -2 12 34 63 HIKE
double.flf (FIGlet font)

figlet conversion by <PERSON>, <EMAIL>, 11/24/94

From: <EMAIL> (<PERSON>)
Newsgroups: alt.ascii-art
Subject: FONT: double (not .flf)
Date: 15 Mar 1994 16:20:01 -0000
Organization: Commodore 64 Services, University of Warwick, UK
Lines: 30
Message-ID: <2m4n7h$<EMAIL>>
NNTP-Posting-Host: basil.csv.warwick.ac.uk
Mime-Version: 1.0
Content-Type: text/plain; charset=US-ASCII
Content-Transfer-Encoding: 7bit
Keywords: font
Status: RO

Below is the capital letters for a font & also a few punctuation characters
(no lower case).

$ $@
$ $@
$ $@
$ $@
$ $@@
 __@
 ||@
 ||@
 ..@
   @@
  _@
 //@
   @
   @
   @@
  __ __ @
  || || @
 =||=||=@
  || || @
        @@
 @
 @
 @
 @
 @@
    _@
 O //@
  // @
 // O@
     @@
 @
 @
 @
 @
 @@
 //@
   @
   @
   @
   @@
   _@
  //@
 (( @
  \\\\@
    @@
 _  @
 \\\\ @
  ))@
 // @
    @@
 @
 @
 @
 @
 @@
 @
 @
 @
 @
 @@
   @
   @
   @
 //@
   @@
 @
 @
 @
 @
 @@
   @
   @
   @
 ||@
   @@
 @
 @
 @
 @
 @@
 @
 @
 @
 @
 @@
 @
 @
 @
 @
 @@
 @
 @
 @
 @
 @@
 @
 @
 @
 @
 @@
 @
 @
 @
 @
 @@
 @
 @
 @
 @
 @@
 @
 @
 @
 @
 @@
 @
 @
 @
 @
 @@
 @
 @
 @
 @
 @@
 @
 @
 @
 @
 @@
 @
 @
 @
 @
 @@
 @
 @
 @
 @
 @@
 @
 @
 @
 @
 @@
 @
 @
 @
 @
 @@
 @
 @
 @
 @
 @@
 ____ @
 |  \\\\@
   _//@
   || @
      @@
 @
 @
 @
 @
 @@
  ___ @
 // \\\\@
 ||=||@
 || ||@
      @@
 ____ @
 || ))@
 ||=) @
 ||_))@
      @@
   ___@
  //  @
 ((   @
  \\\\__@
      @@
 ____  @
 || \\\\ @
 ||  ))@
 ||_// @
       @@
  ____@
 ||   @
 ||== @
 ||___@
      @@
  ____@
 ||   @
 ||== @
 ||   @
      @@
   ___ @
  // \\\\@
 (( ___@
  \\\\_||@
       @@
 __  __@
 ||  ||@
 ||==||@
 ||  ||@
       @@
 __@
 ||@
 ||@
 ||@
   @@
    __@
    ||@
    ||@
 |__||@
      @@
 __ __@
 || //@
 ||<< @
 || \\\\@
      @@
 __   @
 ||   @
 ||   @
 ||__|@
      @@
 ___  ___@
 ||\\\\//||@
 || \\/ ||@
 ||    ||@
         @@
 __  __@
 ||\\ ||@
 ||\\\\||@
 || \\||@
       @@
   ___  @
  // \\\\ @
 ((   ))@
  \\\\_// @
        @@
 ____ @
 || \\\\@
 ||_//@
 ||   @
      @@
   ___  @
  // \\\\ @
 ((   ))@
  \\\\_/X|@
        @@
 ____ @
 || \\\\@
 ||_//@
 || \\\\@
      @@
  __ @
 (( \\@
  \\\\ @
 \\_))@
     @@
 ______@
 | || |@
   ||  @
   ||  @
       @@
 __ __@
 || ||@
 || ||@
 \\\\_//@
      @@
 __ __@
 || ||@
 \\\\ //@
  \\V/ @
      @@
 __    __@
 ||    ||@
 \\\\ /\\ //@
  \\V/\\V/ @
         @@
 _   _@
 \\\\ //@
  )X( @
 // \\\\@
      @@
 _  _@
 \\\\//@
  )/ @
 //  @
     @@
 ____@
   //@
  // @
 //__@
     @@
 @
 @
 @
 @
 @@
 @
 @
 @
 @
 @@
 @
 @
 @
 @
 @@
 @
 @
 @
 @
 @@
 @
 @
 @
 @
 @@
 @
 @
 @
 @
 @@
  ___ @
 // \\\\@
 ||=||@
 || ||@
      @@
 ____ @
 || ))@
 ||=) @
 ||_))@
      @@
   ___@
  //  @
 ((   @
  \\\\__@
      @@
 ____  @
 || \\\\ @
 ||  ))@
 ||_// @
       @@
  ____@
 ||   @
 ||== @
 ||___@
      @@
  ____@
 ||   @
 ||== @
 ||   @
      @@
   ___ @
  // \\\\@
 (( ___@
  \\\\_||@
       @@
 __  __@
 ||  ||@
 ||==||@
 ||  ||@
       @@
 __@
 ||@
 ||@
 ||@
   @@
    __@
    ||@
    ||@
 |__||@
      @@
 __ __@
 || //@
 ||<< @
 || \\\\@
      @@
 __   @
 ||   @
 ||   @
 ||__|@
      @@
 ___  ___@
 ||\\\\//||@
 || \\/ ||@
 ||    ||@
         @@
 __  __@
 ||\\ ||@
 ||\\\\||@
 || \\||@
       @@
   ___  @
  // \\\\ @
 ((   ))@
  \\\\_// @
        @@
 ____ @
 || \\\\@
 ||_//@
 ||   @
      @@
   ___  @
  // \\\\ @
 ((   ))@
  \\\\_/X|@
        @@
 ____ @
 || \\\\@
 ||_//@
 || \\\\@
      @@
  __ @
 (( \\@
  \\\\ @
 \\_))@
     @@
 ______@
 | || |@
   ||  @
   ||  @
       @@
 __ __@
 || ||@
 || ||@
 \\\\_//@
      @@
 __ __@
 || ||@
 \\\\ //@
  \\V/ @
      @@
 __    __@
 ||    ||@
 \\\\ /\\ //@
  \\V/\\V/ @
         @@
 _   _@
 \\\\ //@
  )X( @
 // \\\\@
      @@
 _  _@
 \\\\//@
  )/ @
 //  @
     @@
 ____@
   //@
  // @
 //__@
     @@
 @
 @
 @
 @
 @@
 @
 @
 @
 @
 @@
 @
 @
 @
 @
 @@
 @
 @
 @
 @
 @@
 @
 @
 @
 @
 @@
 @
 @
 @
 @
 @@
 @
 @
 @
 @
 @@
 @
 @
 @
 @
 @@
 @
 @
 @
 @
 @@
 @
 @
 @
 @
 @@
 @
 @
 @
 @
 @@
`