export default `flf2a$ 7 6 16 1 16 0 129 0
Author : Myflix
Date   : 2003/10/18 21:37:14
Version: 1.0
-------------------------------------------------

-------------------------------------------------
This font has been created using <PERSON><PERSON><PERSON>'s FIGlet font export assistant.
Have a look at: http://www.jave.de

Permission is hereby given to modify this font, as long as the
modifier's name is placed on a comment line.

---

Font modified June 17, 2007 by patorjk 
This was to widen the space character.
$  $#
$  $#
$  $#
$  $#
$  $#
$  $#
$  $##
  _    #
U|"|u  #
\\| |/  #
 |_|   #
 (_)   #
 |||_  #
(__)_) ##
"#
 #
 #
 #
 #
 #
 ##
##
 #
 #
 #
 #
 #
 ##
$#
 #
 #
 #
 #
 #
 ##
%#
 #
 #
 #
 #
 #
 ##
&#
 #
 #
 #
 #
 #
 ##
 _  #
|"| #
|_| #
    #
    #
    #
    ##
(#
 #
 #
 #
 #
 #
 ##
)#
 #
 #
 #
 #
 #
 ##
*#
 #
 #
 #
 #
 #
 ##
+#
 #
 #
 #
 #
 #
 ##
    #
    #
    #
    #
 _  #
(") #
 \\| ##
        #
        #
 U  u   #
 /___\\  #
|__"__| #
        #
        ##
    #
    #
    #
    #
 _  #
(") #
 "  ##
/#
 #
 #
 #
 #
 #
 ##
  ___      #
 / _"\\  u  #
| / U |/   #
| \\// |,-. #
 \\___/(_/  #
  //       #
 (__)      ##
    _    #
   /"|   #
 u | |u  #
  \\| |/  #
   |_|   #
 _//<,-, #
(__)(_/  ##
  ____    #
 |___"\\   #
 U __) |  #
 \\/ __/ \\ #
 |_____|u #
 <<  //   #
(__)(__)  ##
 _____   #
|___"/u  #
U_|_ \\/  #
 ___) |  #
|____/   #
 _// \\\\  #
(__)(__) ##
 _  _    #
| ||"|   #
| || |_  #
|__   _| #
  /|_|\\  #
 u_|||_u #
 (__)__) ##
  ____    #
U|"___|u  #
\\|___ \\/  #
  ___) |  #
 |____/   #
,-,>>\\,-. #
 \\ ) (_/  ##
   __     #
U /"/_ u  #
\\| '_ \\/  #
 | (_) |  #
  \\___/   #
 _// \\\\_  #
(__) (__) ##
  _____  #
 |___ "| #
    / /  #
 u// /\\  #
  /_/ U  #
 <<>>_   #
(__)__)  ##
  ___     #
U( " ) u  #
\\/   \\/   #
| ( ) |   #
 \\___/>>  #
  )( (__) #
 (__)     ##
   ___    #
  / _"\\   #
 | (_) |  #
 /\\__, |\\ #
U<< |_/ u #
(__) )(   #
    (__)  ##
    #
 _  #
(") #
    #
 _  #
(") #
    ##
;#
 #
 #
 #
 #
 #
 ##
<#
 #
 #
 #
 #
 #
 ##
=#
 #
 #
 #
 #
 #
 ##
>#
 #
 #
 #
 #
 #
 ##
  ___    #
 |__"\\   #
U  / /u  #
 \\|_|/   #
  (_)    #
 _//\\,-. #
(__)( /  ##
@#
 #
 #
 #
 #
 #
 ##
    _      #
U  /"\\  u  #
 \\/ _ \\/   #
 / ___ \\   #
/_/   \\_\\  #
 \\\\    >>  #
(__)  (__) ##
   ____   #
U | __")u #
 \\|  _ \\/ #
  | |_) | #
  |____/  #
 _|| \\\\_  #
(__) (__) ##
   ____  #
U /"___| #
\\| | u   #
 | |/__  #
  \\____| #
 _// \\\\  #
(__)(__) ##
  ____    #
 |  _"\\   #
/| | | |  #
U| |_| |\\ #
 |____/ u #
  |||_    #
 (__)_)   ##
U _____ u #
\\| ___"|/ #
 |  _|"   #
 | |___   #
 |_____|  #
 <<   >>  #
(__) (__) ##
  _____  #
 |" ___| #
U| |_  u #
\\|  _|/  #
 |_|     #
 )(\\\\,-  #
(__)(_/  ##
   ____   #
U /"___|u #
\\| |  _ / #
 | |_| |  #
  \\____|  #
  _)(|_   #
 (__)__)  ##
  _   _   #
 |'| |'|  #
/| |_| |\\ #
U|  _  |u #
 |_| |_|  #
 //   \\\\  #
(_") ("_) ##
              #
     ___      #
    |_"_|     #
     | |      #
   U/| |\\u    #
.-,_|___|_,-. #
 \\_)-' '-(_/  ##
     _      #
  U |"| u   #
 _ \\| |/    #
| |_| |_,-. #
 \\___/-(_/  #
  _//       #
 (__)       ##
   _  __    #
  |"|/ /    #
  | ' /     #
U/| . \\\\u   #
  |_|\\_\\    #
,-,>> \\\\,-. #
 \\.)   (_/  ##
   _      #
  |"|     #
U | | u   #
 \\| |/__  #
  |_____| #
  //  \\\\  #
 (_")("_) ##
  __  __   #
U|' \\/ '|u #
\\| |\\/| |/ #
 | |  | |  #
 |_|  |_|  #
<<,-,,-.   #
 (./  \\.)  ##
  _   _     #
 | \\ |"|    #
<|  \\| |>   #
U| |\\  |u   #
 |_| \\_|    #
 ||   \\\\,-. #
 (_")  (_/  ##
   U  ___ u #
    \\/"_ \\/ #
    | | | | #
.-,_| |_| | #
 \\_)-\\___/  #
      \\\\    #
     (__)   ##
  ____    #
U|  _"\\ u #
\\| |_) |/ #
 |  __/   #
 |_|      #
 ||>>_    #
(__)__)   ##
   ___    #
  / " \\   #
 | |"| |  #
/| |_| |\\ #
U \\__\\_\\u #
   \\\\//   #
  (_(__)  ##
   ____     #
U |  _"\\ u  #
 \\| |_) |/  #
  |  _ <    #
  |_| \\_\\   #
  //   \\\\_  #
 (__)  (__) ##
  ____     #
 / __"| u  #
<\\___ \\/   #
 u___) |   #
 |____/>>  #
  )(  (__) #
 (__)      ##
  _____   #
 |_ " _|  #
   | |    #
  /| |\\   #
 u |_|U   #
 _// \\\\_  #
(__) (__) ##
   _   _  #
U |"|u| | #
 \\| |\\| | #
  | |_| | #
 <<\\___/  #
(__) )(   #
    (__)  ##
 __     __   #
 \\ \\   /"/u  #
  \\ \\ / //   #
  /\\ V /_,-. #
 U  \\_/-(_/  #
   //        #
  (__)       ##
              #
 __        __ #
 \\"\\      /"/ #
 /\\ \\ /\\ / /\\ #
U  \\ V  V /  U#
.-,_\\ /\\ /_,-.#
 \\_)-'  '-(_/ ##
  __  __   #
  \\ \\/"/   #
  /\\  /\\   #
 U /  \\ u  #
  /_/\\_\\   #
,-,>> \\\\_  #
 \\_)  (__) ##
  __   __ #
  \\ \\ / / #
   \\ V /  #
  U_|"|_u #
    |_|   #
.-,//|(_  #
 \\_) (__) ##
  _____  #
 |"_  /u #
 U / //  #
 \\/ /_   #
 /____|  #
 _//<<,- #
(__) (_/ ##
[#
 #
 #
 #
 #
 #
 ##
\\#
 #
 #
 #
 #
 #
 ##
]#
 #
 #
 #
 #
 #
 ##
U _ u #
\\/"\\/ #
|/\`\\| #
      #
      #
      #
      ##
_#
 #
 #
 #
 #
 #
 ##
 ___ #
(" / #
 )/  #
     #
     #
     #
     ##
    _      #
U  /"\\  u  #
 \\/ _ \\/   #
 / ___ \\   #
/_/   \\_\\  #
 \\\\    >>  #
(__)  (__) ##
   ____   #
U | __")u #
 \\|  _ \\/ #
  | |_) | #
  |____/  #
 _|| \\\\_  #
(__) (__) ##
   ____  #
U /"___| #
\\| | u   #
 | |/__  #
  \\____| #
 _// \\\\  #
(__)(__) ##
  ____    #
 |  _"\\   #
/| | | |  #
U| |_| |\\ #
 |____/ u #
  |||_    #
 (__)_)   ##
U _____ u #
\\| ___"|/ #
 |  _|"   #
 | |___   #
 |_____|  #
 <<   >>  #
(__) (__) ##
  _____  #
 |" ___| #
U| |_  u #
\\|  _|/  #
 |_|     #
 )(\\\\,-  #
(__)(_/  ##
   ____   #
U /"___|u #
\\| |  _ / #
 | |_| |  #
  \\____|  #
  _)(|_   #
 (__)__)  ##
  _   _   #
 |'| |'|  #
/| |_| |\\ #
U|  _  |u #
 |_| |_|  #
 //   \\\\  #
(_") ("_) ##
              #
     ___      #
    |_"_|     #
     | |      #
   U/| |\\u    #
.-,_|___|_,-. #
 \\_)-' '-(_/  ##
     _      #
  U |"| u   #
 _ \\| |/    #
| |_| |_,-. #
 \\___/-(_/  #
  _//       #
 (__)       ##
   _  __    #
  |"|/ /    #
  | ' /     #
U/| . \\\\u   #
  |_|\\_\\    #
,-,>> \\\\,-. #
 \\.)   (_/  ##
   _      #
  |"|     #
U | | u   #
 \\| |/__  #
  |_____| #
  //  \\\\  #
 (_")("_) ##
  __  __   #
U|' \\/ '|u #
\\| |\\/| |/ #
 | |  | |  #
 |_|  |_|  #
<<,-,,-.   #
 (./  \\.)  ##
  _   _     #
 | \\ |"|    #
<|  \\| |>   #
U| |\\  |u   #
 |_| \\_|    #
 ||   \\\\,-. #
 (_")  (_/  ##
   U  ___ u #
    \\/"_ \\/ #
    | | | | #
.-,_| |_| | #
 \\_)-\\___/  #
      \\\\    #
     (__)   ##
  ____    #
U|  _"\\ u #
\\| |_) |/ #
 |  __/   #
 |_|      #
 ||>>_    #
(__)__)   ##
   ___    #
  / " \\   #
 | |"| |  #
/| |_| |\\ #
U \\__\\_\\u #
   \\\\//   #
  (_(__)  ##
   ____     #
U |  _"\\ u  #
 \\| |_) |/  #
  |  _ <    #
  |_| \\_\\   #
  //   \\\\_  #
 (__)  (__) ##
  ____     #
 / __"| u  #
<\\___ \\/   #
 u___) |   #
 |____/>>  #
  )(  (__) #
 (__)      ##
  _____   #
 |_ " _|  #
   | |    #
  /| |\\   #
 u |_|U   #
 _// \\\\_  #
(__) (__) ##
   _   _  #
U |"|u| | #
 \\| |\\| | #
  | |_| | #
 <<\\___/  #
(__) )(   #
    (__)  ##
 __     __   #
 \\ \\   /"/u  #
  \\ \\ / //   #
  /\\ V /_,-. #
 U  \\_/-(_/  #
   //        #
  (__)       ##
              #
 __        __ #
 \\"\\      /"/ #
 /\\ \\ /\\ / /\\ #
U  \\ V  V /  U#
.-,_\\ /\\ /_,-.#
 \\_)-'  '-(_/ ##
  __  __   #
  \\ \\/"/   #
  /\\  /\\   #
 U /  \\ u  #
  /_/\\_\\   #
,-,>> \\\\_  #
 \\_)  (__) ##
  __   __ #
  \\ \\ / / #
   \\ V /  #
  U_|"|_u #
    |_|   #
.-,//|(_  #
 \\_) (__) ##
  _____  #
 |"_  /u #
 U / //  #
 \\/ /_   #
 /____|  #
 _//<<,- #
(__) (_/ ##
    __  #
u  /"/U #
 \\| |/  #
 < <    #
  | |   #
 <<\\_\\  #
(__)_)  ##
|#
 #
 #
 #
 #
 #
 ##
__      #
\\"\\  u  #
 | |/   #
/ > >   #
U| |    #
/_/>>_  #
 (_(__) ##
~#
 #
 #
 #
 #
 #
 ##
    _      #
U  /"\\  u  #
 \\/ _ \\/   #
 / ___ \\   #
/_/   \\_\\  #
 \\\\    >>  #
(__)  (__) ##
   U  ___ u #
    \\/"_ \\/ #
    | | | | #
.-,_| |_| | #
 \\_)-\\___/  #
      \\\\    #
     (__)   ##
   _   _  #
U |"|u| | #
 \\| |\\| | #
  | |_| | #
 <<\\___/  #
(__) )(   #
    (__)  ##
    _      #
U  /"\\  u  #
 \\/ _ \\/   #
 / ___ \\   #
/_/   \\_\\  #
 \\\\    >>  #
(__)  (__) ##
   U  ___ u #
    \\/"_ \\/ #
    | | | | #
.-,_| |_| | #
 \\_)-\\___/  #
      \\\\    #
     (__)   ##
   _   _  #
U |"|u| | #
 \\| |\\| | #
  | |_| | #
 <<\\___/  #
(__) )(   #
    (__)  ##
�#
 #
 #
 #
 #
 #
 ##
`