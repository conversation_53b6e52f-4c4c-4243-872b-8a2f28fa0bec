export default `flf2a$ 5 4 11 0 16 0 8256 0
Author : <PERSON><PERSON>
Date   : 2004/4/9 13:04:21
Version: 1.0
-------------------------------------------------

-------------------------------------------------
This font has been created using <PERSON><PERSON><PERSON>'s FIGlet font export assistant.
Have a look at: http://www.jave.de

Permission is hereby given to modify this font, as long as the
modifier's name is placed on a comment line.

---

Font modified June 17, 2007 by patorjk 
This was to widen the space character.
$  $#
$  $#
$  $#
$  $#
$  $##
.-. #
| | #
{ } #
\`-' #
    ##
.-..-. #
{ }{ } #
\`-'\`-' #
       #
       ##
 _ .-..-.#
{_/ // /}#
{/ // /_}#
 \`-'\`-'  #
         ##
 .--//. #
{ {//-\` #
.-//} } #
\`//--'  #
        ##
 _  .-.  #
{_}/ / _ #
   \\ }{_}#
   \`-'   #
         ##
&#
 #
 #
 #
 ##
 .-. #
 { } #
 \`-' #
     #
     ##
  .'} #
 /.'  #
{ \`.  #
 \`-.} #
      ##
{\`.   #
 \`.\\  #
 .' } #
{.-'  #
      ##
*#
 #
 #
 #
 ##
   _    #
 _{ }_  #
{_   _} #
  {_}   #
        ##
    #
    #
 _  #
/_} #
    ##
      #
 ___  #
{___} #
      #
      ##
    #
    #
 _  #
{_} #
    ##
   .-. #
  / /  #
 / /   #
\`-'    #
       ##
 .---.  #
. .-. . #
' \`-' ' #
 \`---'  #
        ##
.-. #
{ | #
| } #
\`-' #
    ##
.---.  #
\`-\`} } #
{ {.-. #
 \`---' #
       ##
.---.  #
\`-\`} } #
.-.} } #
\`----\` #
       ##
.-. .-. #
 \\ \\| | #
  \`-\\ } #
    \`-' #
        ##
 .---. #
{ {\`-' #
.-.} } #
\`---'  #
       ##
  .-.  #
 / /.  #
{ {} } #
 \`--'  #
       ##
.---.  #
\`-\`} } #
  / /  #
 \`-'   #
       ##
 .--.  #
{ {} } #
{ {} } #
 \`--'  #
       ##
 .--.  #
{ {} } #
 \`/ /  #
 \`-'   #
       ##
 _  #
{_} #
 _  #
{_} #
    ##
 _  #
{_} #
 _  #
/_} #
    ##
 .-. #
/ {  #
\\ {  #
 \`-\` #
     ##
 ___  #
{___} #
 ___  #
{___} #
      ##
.-.  #
 } \\ #
 } / #
'-'  #
     ##
.---.  #
\`-\`} } #
  { }  #
  \`-'  #
       ##
  .----. #
 / .--. \\#
 |/ {} \\|#
 \\\`-'\`-'/#
  \`----' ##
  .--.   #
 / {} \\  #
/  /\\  \\ #
\`-'  \`-' #
         ##
.----.  #
| {_} } #
| {_} } #
\`----'  #
        ##
.----. #
| }\`-' #
| },-. #
\`----' #
       ##
.----.  #
} {-. \\ #
} '-} / #
\`----'  #
        ##
.----. #
} |__} #
} '__} #
\`----' #
       ##
.----. #
} |__} #
} '_}  #
\`--'   #
       ##
.----. #
| |--' #
| }-\`} #
\`----' #
       ##
.-. .-. #
{ {_} | #
| { } } #
\`-' \`-' #
        ##
.-. #
{ | #
| } #
\`-' #
    ##
   .-. #
   | | #
{\`-' } #
 \`---' #
       ##
.-..-. #
| ' /  #
| . \\  #
\`-'\`-\` #
       ##
.-.    #
} |    #
} '--. #
\`----' #
       ##
.-.  .-. #
}  \\/  { #
| {  } | #
\`-'  \`-' #
         ##
.-. .-. #
|  \\{ | #
| }\\  { #
\`-' \`-' #
        ##
 .---.  #
/ {-. \\ #
\\ '-} / #
 \`---'  #
        ##
.-.-.  #
| } }} #
| |-'  #
\`-'    #
       ##
 .---.  #
/ {-. \\ #
\\ '-} { #
 \`--\`-' #
        ##
.---.  #
} }}_} #
| } \\  #
\`-'-'  #
       ##
 .----. #
{ {__-\` #
.-._} } #
\`----'  #
        ##
.-----. #
\`-' '-' #
  } {   #
  \`-'   #
        ##
.-. .-. #
| } { | #
\\ \`-' / #
 \`---'  #
        ##
.-.   .-.#
 \\ \\_/ / #
  \\   /  #
   \`-'   #
         ##
.-.  .-. #
| {  } | #
{  /\\  } #
\`-'  \`-' #
         ##
.-..-. #
\\ {} / #
/ {} \\ #
\`-'\`-' #
       ##
.-.  .-. #
 \\ \\/ /  #
  \`-\\ }  #
    \`-'  #
         ##
.---.  #
\`-\`} } #
{ /.-. #
 \`---' #
       ##
 .---, #
{ .-'  #
{ \`-.  #
 \`---\` #
       ##
.-.    #
 \\ \\   #
  \\ \\  #
   \`-' #
       ##
.---.  #
 \`-. } #
 .-' } #
\`---\`  #
       ##
  .--.   #
 / {} \\  #
\`-'  \`-' #
         #
         ##
         #
 $     $ #
 $     $ #
 _______ #
{_______}##
.-.   #
 \\ \\  #
  \`-' #
  $$  #
      ##
  .--.   #
 / {} \\  #
/  /\\  \\ #
\`-'  \`-' #
         ##
.----.  #
| {_} } #
| {_} } #
\`----'  #
        ##
.----. #
| }\`-' #
| },-. #
\`----' #
       ##
.----.  #
} {-. \\ #
} '-} / #
\`----'  #
        ##
.----. #
} |__} #
} '__} #
\`----' #
       ##
.----. #
} |__} #
} '_}  #
\`--'   #
       ##
.----. #
| |--' #
| }-\`} #
\`----' #
       ##
.-. .-. #
{ {_} | #
| { } } #
\`-' \`-' #
        ##
.-. #
{ | #
| } #
\`-' #
    ##
   .-. #
   | | #
{\`-' } #
 \`---' #
       ##
.-..-. #
| ' /  #
| . \\  #
\`-'\`-\` #
       ##
.-.    #
} |    #
} '--. #
\`----' #
       ##
.-.  .-. #
}  \\/  { #
| {  } | #
\`-'  \`-' #
         ##
.-. .-. #
|  \\{ | #
| }\\  { #
\`-' \`-' #
        ##
 .---.  #
/ {-. \\ #
\\ '-} / #
 \`---'  #
        ##
.-.-.  #
| } }} #
| |-'  #
\`-'    #
       ##
 .---.  #
/ {-. \\ #
\\ '-} { #
 \`--\`-' #
        ##
.---.  #
} }}_} #
| } \\  #
\`-'-'  #
       ##
 .----. #
{ {__-\` #
.-._} } #
\`----'  #
        ##
.-----. #
\`-' '-' #
  } {   #
  \`-'   #
        ##
.-. .-. #
| } { | #
\\ \`-' / #
 \`---'  #
        ##
.-.   .-.#
 \\ \\_/ / #
  \\   /  #
   \`-'   #
         ##
.-.  .-. #
| {  } | #
{  /\\  } #
\`-'  \`-' #
         ##
.-..-. #
\\ {} / #
/ {} \\ #
\`-'\`-' #
       ##
.-.  .-. #
 \\ \\/ /  #
  \`-\\ }  #
    \`-'  #
         ##
.---.  #
\`-\`} } #
{ /.-. #
 \`---' #
       ##
   .'} #
 .'.'  #
{  \`.  #
 \`-._} #
       ##
.-. #
{ } #
{ } #
\`-' #
    ##
{\`.    #
 \`.\`.  #
 .'  } #
{_.-'  #
       ##
       #
{\`-._  #
 \`-._} #
       #
       ##
 {}--{}  #
 / {} \\  #
/  /\\  \\ #
\`-'  \`-' #
         ##
{_}-{_} #
/ {-. \\ #
\\ '-} / #
 \`---'  #
        ##
{_} {_} #
| } { | #
\\ \`-' / #
 \`---'  #
        ##
 {}--{}  #
 / {} \\  #
/  /\\  \\ #
\`-'  \`-' #
         ##
{_}-{_} #
/ {-. \\ #
\\ '-} / #
 \`---'  #
        ##
{_} {_} #
| } { | #
\\ \`-' / #
 \`---'  #
        ##
.----.  #
| {_} } #
| {_} } #
| |--'  #
\`-'     ##`