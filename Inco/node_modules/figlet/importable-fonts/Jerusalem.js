export default `flf2a$ 7 6 100 15 31 1
Jerusalem by <PERSON><PERSON><PERSON><PERSON> - based on Standard by <PERSON><PERSON> & <PERSON>
Questions and comments regarding jerusalem.<NAME_EMAIL>
Modified for figlet 2.1 by <PERSON> 16 Dec 1993
Date: 13 Feb 1994

Explanation of first line:
flf2 - "magic number" for file identification
a    - should always be \`a', for now
$    - the "hardblank" -- prints as a blank, but can't be smushed
7    - height of a character
6    - height of a character, not including descenders
100  -  max line length (excluding comment lines) + a fudge factor
15   - default smushmode for this font
31   - number of comment lines
1    - print right-to-left (figlet 2.1 or later only)

Hebrew keyboard maps:

All capital letters print the english character for that key.  All numbers 
and symbols are the same in in Hebrew/English except for those shown here.
The * symbol prints as a Jewish star (Star of David).
While in figlet, type ~ on a line by itself to see this list:

aleph   = t | zayin    = z | lamed   = k | ayin    = g | shin/sin  = a
bet/vet = c | chet     = j | mem     = n | pey/fey = p | taf/saf   = ,
gimmel  = d | tet      = y | final " = o | final " = ; | ,(comma)  = '
daled   = s | yud      = h | nun     = b | tzaadi  = m | .(period) = /
hay     = v | kaf/chaf = f | final " = i | final " = . | ;         = \`
vav     = u | final "  = l | samekh  = x | kuf     = e | /         = q
The asterisk (*) is a Star of David      | resh    = r | '(apost.) = w

$@
$@
$@
$@
$@
$@
$@@
   @
 _ @
| |@
| |@
|_|@
(_)@
   @@
     @
 _ _ @
( | )@
 V V @
  $  @
  $  @
     @@
          @
   _  _   @
 _| || |_ @
|_  ..  _|@
|_      _|@
  |_||_|  @
          @@
     @
  _  @
 | | @
/ __)@
\\__ \\@
(   /@
 |_| @@
      @
 _  __@
(_)/ /@
  / / @
 / /_ @
/_/(_)@
      @@
        @
  ___   @
 ( _ )  @
 / _ \\/\\@
| (_>  <@
 \\___/\\/@
        @@
   @
   @
   @
   @
 _ @
( )@
|/ @@
    @
  __@
 / /@
| | @
| | @
| | @
 \\_\\@@
    @
__  @
\\ \\ @
 | |@
 | |@
 | |@
/_/ @@
      @
      @
__/\\__@
\\    /@
/_  _\\@
  \\/  @
      @@
       @
       @
   _   @
 _| |_ @
|_   _|@
  |_|  @
       @@
          @
   ______ @
  |  __  |@
  | |  | |@
 _| |  | |@
|___|  |_|@
          @@
       @
       @
       @
 _____ @
|_____|@
   $   @
       @@
        @
 __   _ @
|. | | |@
 | | // @
 | |//  @
 | |    @
 |_|    @@
   @
   @
   @
   @
 _ @
(_)@
   @@
       @
  ___  @
 / _ \\ @
| | | |@
| |_| |@
 \\___/ @
       @@
   @
 _ @
/ |@
| |@
| |@
|_|@
   @@
       @
 ____  @
|___ \\ @
  __) |@
 / __/ @
|_____|@
       @@
       @
 _____ @
|___ / @
  |_ \\ @
 ___) |@
|____/ @
       @@
        @
 _  _   @
| || |  @
| || |_ @
|__   _|@
   |_|  @
        @@
       @
 ____  @
| ___| @
|___ \\ @
 ___) |@
|____/ @
       @@
       @
  __   @
 / /_  @
| '_ \\ @
| (_) |@
 \\___/ @
       @@
       @
 _____ @
|___  |@
   / / @
  / /  @
 /_/   @
       @@
       @
  ___  @
 ( _ ) @
 / _ \\ @
| (_) |@
 \\___/ @
       @@
       @
  ___  @
 / _ \\ @
| (_) |@
 \\__, |@
   /_/ @
       @@
   @
   @
 _ @
(_)@
 _ @
(_)@
   @@
        @
 ______ @
|  __  |@
| |_ | |@
|___|| |@
     | |@
     |_|@@
    @
  __@
 / /@
/ / @
\\ \\ @
 \\_\\@
    @@
       @
       @
 _____ @
|_____|@
|_____|@
   $   @
       @@
    @
__  @
\\ \\ @
 \\ \\@
 / /@
/_/ @
    @@
     @
 ___ @
|__ \\@
  / /@
 |_| @
 (_) @
     @@
         @
   ____  @
  / __ \\ @
 / / _\` |@
| | (_| |@
 \\ \\__,_|@
  \\____/ @@
         @
    _    @
   / \\   @
  / _ \\  @
 / ___ \\ @
/_/   \\_\\@
         @@
       @
 ____  @
| __ ) @
|  _ \\ @
| |_) |@
|____/ @
       @@
       @
  ____ @
 / ___|@
| |    @
| |___ @
 \\____|@
       @@
       @
 ____  @
|  _ \\ @
| | | |@
| |_| |@
|____/ @
       @@
       @
 _____ @
| ____|@
|  _|  @
| |___ @
|_____|@
       @@
       @
 _____ @
|  ___|@
| |_   @
|  _|  @
|_|    @
       @@
       @
  ____ @
 / ___|@
| |  _ @
| |_| |@
 \\____|@
       @@
       @
 _   _ @
| | | |@
| |_| |@
|  _  |@
|_| |_|@
       @@
     @
 ___ @
|_ _|@
 | | @
 | | @
|___|@
     @@
       @
     _ @
    | |@
 _  | |@
| |_| |@
 \\___/ @
       @@
      @
 _  __@
| |/ /@
| ' / @
| . \\ @
|_|\\_\\@
      @@
       @
 _     @
| |    @
| |    @
| |___ @
|_____|@
       @@
        @
 __  __ @
|  \\/  |@
| |\\/| |@
| |  | |@
|_|  |_|@
        @@
       @
 _   _ @
| \\ | |@
|  \\| |@
| |\\  |@
|_| \\_|@
       @@
       @
  ___  @
 / _ \\ @
| | | |@
| |_| |@
 \\___/ @
       @@
       @
 ____  @
|  _ \\ @
| |_) |@
|  __/ @
|_|    @
       @@
       @
  ___  @
 / _ \\ @
| | | |@
| |_| |@
 \\__\\_\\@
       @@
       @
 ____  @
|  _ \\ @
| |_) |@
|  _ < @
|_| \\_\\@
       @@
       @
 ____  @
/ ___| @
\\___ \\ @
 ___) |@
|____/ @
       @@
       @
 _____ @
|_   _|@
  | |  @
  | |  @
  |_|  @
       @@
       @
 _   _ @
| | | |@
| | | |@
| |_| |@
 \\___/ @
       @@
         @
__     __@
\\ \\   / /@
 \\ \\ / / @
  \\ V /  @
   \\_/   @
         @@
            @
__        __@
\\ \\      / /@
 \\ \\ /\\ / / @
  \\ V  V /  @
   \\_/\\_/   @
            @@
      @
__  __@
\\ \\/ /@
 \\  / @
 /  \\ @
/_/\\_\\@
      @@
       @
__   __@
\\ \\ / /@
 \\ V / @
  | |  @
  |_|  @
       @@
      @
 _____@
|__  /@
  / / @
 / /_ @
/____|@
      @@
    @
 __ @
| _|@
| | @
| | @
| | @
|__|@@
      @
__    @
\\ \\   @
 \\ \\  @
  \\ \\ @
   \\_\\@
      @@
    @
 __ @
|_ |@
 | |@
 | |@
 | |@
|__|@@
    @
 /\\ @
|/\\|@
  $ @
  $ @
  $ @
    @@
       @
       @
       @
       @
       @
 _____ @
|_____|@@
   @
   @
 _ @
(_)@
 _ @
( )@
|/ @@
           @
 _   _   _ @
| | | | | |@
| | | | | |@
| |/ /_/ / @
|_______/  @
           @@
      @
  ___ @
 |_  |@
   | |@
 __| |@
|____|@
      @@
          @
  ______  @
 |____  | @
      | | @
 _____| |_@
/________/@
          @@
         @
   ____  @
  |__  | @
     | | @
 ____| | @
/____/\\_\\@
         @@
        @
 ______ @
|____  |@
  _  | |@
 | | |_|@
 | |    @
 |_|    @@
        @
 _____  @
|____ \\ @
     | |@
 ____| |@
|_____/ @
        @@
        @
 __   _ @
 \\ \\ | |@
  \\ \\| |@
 __\\ \` |@
|______|@
        @@
     @
 ___ @
|_  |@
  |_|@
   $ @
   $ @
     @@
     @
 ___ @
|_  |@
  | |@
  | |@
  | |@
  |_|@@
         @
 _______ @
|.  __  |@
 | |  | |@
 | |  | |@
 |_|  |_|@
         @@
 _      @
| |____ @
|____  |@
    / / @
   / /  @
  /_/   @
        @@
         @
 _______ @
|____  .|@
     | | @
     | | @
     | | @
     |_| @@
          @
  __   __.@
  \\ \\ / / @
   \\ V /  @
 ___\\  \\  @
|______|  @
          @@
         @
 _______ @
|.  __  |@
 | |  | |@
 | | _| |@
 |_||___|@
         @@
          @
 ________ @
|.  ___  |@
 | |   | |@
 | |___| |@
 |_______|@
          @@
         @
 _______ @
|  ___  |@
 \\_\\  | |@
 _____| |@
|_______|@
         @@
      @
    __@
   / /@
  / / @
 / /  @
/_/   @
      @@
        @
 ______ @
|____  |@
     | |@
     | |@
     |_|@
        @@
         @
 _______ @
|____   |@
     | | @
     | | @
     |_| @
         @@
       @
__   __@
\\ \\ / /@
|  V / @
| |\\ \\ @
|_| \\_\\@
       @@
     @
 ___ @
|_  |@
  | |@
  | |@
  |_|@
     @@
         @
 _______ @
|_____  |@
  _   | |@
 | |  | |@
 |_|  |_|@
         @@
   @
 _ @
( )@
|/ @
 $ @
 $ @
   @@
         @
 _______ @
|.  __  |@
 | |  | |@
 | |__/ |@
 |_____/ @
         @@
          @
 __   ___ @
|. | /_  |@
 | |   | |@
 | |___| |@
 |_______|@
          @@
         @
________ @
\\__   __\\@
   | |   @
   | |   @
   |_|   @
         @@
     @
   __@
  / /@
 | | @
< <  @
 | | @
  \\_\\@@
   @
 _ @
| |@
| |@
| |@
| |@
|_|@@
     @
__   @
\\ \\  @
 | | @
  > >@
 | | @
/_/  @@
aleph   = t | zayin    = z | lamed   = k | ayin    = g | shin/sin  = a@
bet/vet = c | chet     = j | mem     = n | pey/fey = p | taf/saf   = ,@
gimmel  = d | tet      = y | final " = o | final " = ; | ,(comma)  = '@
daled   = s | yud      = h | nun     = b | tzaadi  = m | .(period) = /@
hay     = v | kaf/chaf = f | final " = i | final " = . | ;         = \`@
vav     = u | final "  = l | samekh  = x | kuf     = e | /         = q@
The asterisk (*) is a Star of David      | resh    = r | '(apost.) = w@@
       @
 _   _ @
(_)_(_)@
  /_\\  @
 / _ \\ @
/_/ \\_\\@
       @@
       @
 _   _ @
(_)_(_)@
 / _ \\ @
| |_| |@
 \\___/ @
       @@
       @
 _   _ @
(_) (_)@
| | | |@
| |_| |@
 \\___/ @
       @@
       @
 _   _ @
(_)_(_)@
 / _\` |@
| (_| |@
 \\__,_|@
       @@
       @
 _   _ @
(_)_(_)@
 / _ \\ @
| (_) |@
 \\___/ @
       @@
       @
 _   _ @
(_) (_)@
| | | |@
| |_| |@
 \\__,_|@
       @@
      @
  ___ @
 / _ \\@
| |/ /@
| |\\ \\@
| ||_/@
|_|   @@
`