export default `flf2a$ 4 3 10 -1 20
Cyberfont - large
Figlet conversion by <PERSON>, <EMAIL>, 8-10-94
From: <EMAIL> (Lennert Stock)
Date: 15 Jul 1994 00:04:25 GMT

Here are some fonts. Non-figlet I'm afraid, if you wanna convert them, be
my guest. I posted the isometric fonts before.

------------------------------------------------------------------------------

     .x%%%%%%x.                                             .x%%%%%%x.
    ,%%%%%%%%%%.                                           .%%%%%%%%%%.
   ,%%%'  )'  \\)                                           :(  \`(  \`%%%.
  ,%x%)________) --------- L e n n e r t   S t o c k       ( _   __ (%x%.
  (%%%~^88P~88P|                                           |~=> .=-~ %%%)
  (%%::. .:,\\ .'                                           \`. /,:. .::%%)
  \`;%:\`\\. \`-' |                                             | \`-' ./':%:'
   \`\`x\`. -===.'                   <EMAIL> -------- \`.===- .'x''
    / \`:\`.__.;                                               :.__.':' \\
 .d8b.     ..\`.                                             .'..     .d8b.
 $  $@
 $  $@
 $  $@
 $  $@@
   /@
  / @
 .  @
   $@@
 ..@
 ''@
   @
   @@
@
@
@
@@
@
@
@
@@
@
@
@
@@
@
@
@
@@
 .@
 '@
  @
  @@
@
@
@
@@
@
@
@
@@
@
@
@
@@
@
@
@
@@
 $@
 $@
 .@
 '@@
 $$$@
 ___@
 $$$@
 $$$@@
 $@
 $@
 .@
 $@@
    /@
   / @
  /  @
 /   @@
@
@
@
@@
@
@
@
@@
@
@
@
@@
@
@
@
@@
@
@
@
@@
@
@
@
@@
@
@
@
@@
@
@
@
@@
@
@
@
@@
@
@
@
@@
 $@
 .@
 .@
 $@@
 $@
 .@
 .@
 '@@
@
@
@
@@
@
@
@
@@
@
@
@
@@
 ____ @
  ___|@
  \\   @
   \`  @@
@
@
@
@@
 _______@
 |_____|@
 |     |@
        @@
 ______ @
 |_____]@
 |_____]@
        @@
 _______@
 |     $@
 |_____$@
        @@
 ______$@
 |     \\@
 |_____/@
        @@
 _______@
 |______@
 |______@
        @@
 _______@
 |______@
 |     $@
        @@
  ______@
 |  ____@
 |_____|@
        @@
 _     _@
 |_____|@
 |     |@
        @@
 _____@
   | $@
 __|__@
      @@
 _____@
   | $@
 __| $@
      @@
 _     _@
 |____/$@
 |    \\_@
        @@
 $    $@
 |    $@
 |_____@
       @@
 _______@
 |  |  |@
 |  |  |@
        @@
 __   _@
 | \\  |@
 |  \\_|@
       @@
  _____ @
 |     |@
 |_____|@
        @@
  _____ @
 |_____]@
 |     $@
        @@
  _____ @
 |   __|@
 |____\\|@
        @@
  ______@
 |_____/@
 |    \\_@
        @@
 _______@
 |______@
 ______|@
        @@
 _______@
    |  $@
    |  $@
        @@
 _     _@
 |     |@
 |_____|@
        @@
 _    _@
  \\  /$@
   \\/ $@
       @@
 _  _  _@
 |  |  |@
 |__|__|@
        @@
 _     _@
  \\___/$@
 _/   \\_@
        @@
 __   __@
   \\_/ $@
    |  $@
        @@
 ______@
  ____/@
 /_____@
       @@
@
@
@
@@
 \\   @
  \\  @
   \\ @
    \\@@
@
@
@
@@
@
@
@
@@
      @
      @
 _____@
      @@
 .@
 \`@
  @
  @@
 _______@
 |_____|@
 |     |@
        @@
 ______ @
 |_____]@
 |_____]@
        @@
 _______@
 |     $@
 |_____$@
        @@
 ______$@
 |     \\@
 |_____/@
        @@
 _______@
 |______@
 |______@
        @@
 _______@
 |______@
 |     $@
        @@
  ______@
 |  ____@
 |_____|@
        @@
 _     _@
 |_____|@
 |     |@
        @@
 _____@
   | $@
 __|__@
      @@
 _____@
   | $@
 __| $@
      @@
 _     _@
 |____/$@
 |    \\_@
        @@
 $    $@
 |    $@
 |_____@
       @@
 _______@
 |  |  |@
 |  |  |@
        @@
 __   _@
 | \\  |@
 |  \\_|@
       @@
  _____ @
 |     |@
 |_____|@
        @@
  _____ @
 |_____]@
 |     $@
        @@
  _____ @
 |   __|@
 |____\\|@
        @@
  ______@
 |_____/@
 |    \\_@
        @@
 _______@
 |______@
 ______|@
        @@
 _______@
    |  $@
    |  $@
        @@
 _     _@
 |     |@
 |_____|@
        @@
 _    _@
  \\  /$@
   \\/ $@
       @@
 _  _  _@
 |  |  |@
 |__|__|@
        @@
 _     _@
  \\___/$@
 _/   \\_@
        @@
 __   __@
   \\_/ $@
    |  $@
        @@
 ______@
  ____/@
 /_____@
       @@
@
@
@
@@
  |@
  |@
  |@
  |@@
@
@
@
@@
@
@
@
@@
@
@
@
@@
@
@
@
@@
@
@
@
@@
@
@
@
@@
@
@
@
@@
@
@
@
@@
@
@
@
@@
`