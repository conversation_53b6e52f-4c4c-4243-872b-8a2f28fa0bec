export default `flf2a{ 13 1 3 -1 18 0 24383 0
Author : BIZUN (nefarious of Neurotics)
Date   : 2004/12/19 18:47:06
Version: 1.0
-------------------------------------------------
This font emulates the ICL 1900 Punched Card Code.
Some of us have seen FRTRAN code stored on this things.
I thought, it may be fun to have some. 
Special remarks:
^ stands for "up arrow" char, not in ASCII
\\ stands for "left arrow" char, not in ASCII
| stands for "Pound" char, also not in ASCII
Codes for capital and small letters are doubled for 
convenience.
-------------------------------------------------
This font has been created using JavE's FIGlet font export assistant.
Have a look at: http://www.jave.de
Permission is hereby given to modify this font, as long as the
modifier's name is placed on a comment line.
{\`
{\`
{\`
{\`
{\`
{\`
{\`
{\`
{\`
{\`
{\`
{\`
{\`\`
!\`
*\`
 \`
 \`
 \`
 \`
 \`
 \`
 \`
 \`
*\`
*\`
 \`\`
"\`
 \`
*\`
*\`
 \`
 \`
 \`
 \`
 \`
 \`
 \`
 \`
 \`\`
#\`
 \`
 \`
 \`
 \`
 \`
*\`
 \`
 \`
 \`
 \`
*\`
 \`\`
$\`
 \`
*\`
 \`
 \`
 \`
*\`
 \`
 \`
 \`
 \`
*\`
 \`\`
%\`
 \`
 \`
*\`
 \`
 \`
 \`
*\`
 \`
 \`
 \`
*\`
 \`\`
&\`
*\`
 \`
 \`
 \`
 \`
 \`
 \`
 \`
 \`
 \`
 \`
 \`\`
'\`
 \`
 \`
*\`
 \`
 \`
*\`
 \`
 \`
 \`
 \`
*\`
 \`\`
(\`
 \`
 \`
 \`
 \`
 \`
 \`
 \`
*\`
 \`
 \`
*\`
 \`\`
)\`
 \`
 \`
 \`
 \`
 \`
 \`
 \`
 \`
*\`
 \`
*\`
 \`\`
*\`
 \`
*\`
 \`
 \`
 \`
 \`
*\`
 \`
 \`
 \`
*\`
 \`\`
+\`
*\`
 \`
 \`
 \`
*\`
 \`
 \`
 \`
 \`
 \`
*\`
 \`\`
,\`
*\`
 \`
 \`
 \`
 \`
 \`
 \`
 \`
*\`
 \`
*\`
 \`\`
-\`
 \`
*\`
 \`
 \`
 \`
 \`
 \`
 \`
 \`
 \`
 \`
 \`\`
.\`
*\`
 \`
 \`
 \`
 \`
*\`
 \`
 \`
 \`
 \`
*\`
 \`\`
/\`
 \`
 \`
*\`
*\`
 \`
 \`
 \`
 \`
 \`
 \`
 \`
 \`\`
0\`
 \`
 \`
*\`
 \`
 \`
 \`
 \`
 \`
 \`
 \`
 \`
 \`\`
1\`
 \`
 \`
 \`
*\`
 \`
 \`
 \`
 \`
 \`
 \`
 \`
 \`\`
2\`
 \`
 \`
 \`
 \`
*\`
 \`
 \`
 \`
 \`
 \`
 \`
 \`\`
3\`
 \`
 \`
 \`
 \`
 \`
*\`
 \`
 \`
 \`
 \`
 \`
 \`\`
4\`
 \`
 \`
 \`
 \`
 \`
 \`
*\`
 \`
 \`
 \`
 \`
 \`\`
5\`
 \`
 \`
 \`
 \`
 \`
 \`
 \`
*\`
 \`
 \`
 \`
 \`\`
6\`
 \`
 \`
 \`
 \`
 \`
 \`
 \`
 \`
*\`
 \`
 \`
 \`\`
7\`
 \`
 \`
 \`
 \`
 \`
 \`
 \`
 \`
 \`
*\`
 \`
 \`\`
8\`
 \`
 \`
 \`
 \`
 \`
 \`
 \`
 \`
 \`
 \`
*\`
 \`\`
9\`
 \`
 \`
 \`
 \`
 \`
 \`
 \`
 \`
 \`
 \`
 \`
*\`\`
:\`
*\`
 \`
 \`
 \`
 \`
 \`
 \`
*\`
 \`
 \`
*\`
 \`\`
;\`
*\`
 \`
 \`
 \`
 \`
 \`
*\`
 \`
 \`
 \`
*\`
 \`\`
<\`
 \`
*\`
 \`
 \`
 \`
 \`
 \`
*\`
 \`
 \`
*\`
 \`\`
=\`
 \`
 \`
*\`
 \`
 \`
 \`
 \`
 \`
*\`
 \`
*\`
 \`\`
>\`
 \`
*\`
 \`
 \`
 \`
 \`
 \`
 \`
*\`
 \`
*\`
 \`\`
?\`
 \`
 \`
*\`
 \`
 \`
 \`
 \`
*\`
 \`
 \`
*\`
 \`\`
@\`
 \`
 \`
 \`
 \`
 \`
 \`
*\`
 \`
 \`
 \`
*\`
 \`\`
A\`
*\`
 \`
 \`
*\`
 \`
 \`
 \`
 \`
 \`
 \`
 \`
 \`\`
B\`
*\`
 \`
 \`
 \`
*\`
 \`
 \`
 \`
 \`
 \`
 \`
 \`\`
C\`
*\`
 \`
 \`
 \`
 \`
*\`
 \`
 \`
 \`
 \`
 \`
 \`\`
D\`
*\`
 \`
 \`
 \`
 \`
 \`
*\`
 \`
 \`
 \`
 \`
 \`\`
E\`
*\`
 \`
 \`
 \`
 \`
 \`
 \`
*\`
 \`
 \`
 \`
 \`\`
F\`
*\`
 \`
 \`
 \`
 \`
 \`
 \`
 \`
*\`
 \`
 \`
 \`\`
G\`
*\`
 \`
 \`
 \`
 \`
 \`
 \`
 \`
 \`
*\`
 \`
 \`\`
H\`
*\`
 \`
 \`
 \`
 \`
 \`
 \`
 \`
 \`
 \`
*\`
 \`\`
I\`
*\`
 \`
 \`
 \`
 \`
 \`
 \`
 \`
 \`
 \`
 \`
*\`\`
J\`
 \`
*\`
 \`
*\`
 \`
 \`
 \`
 \`
 \`
 \`
 \`
 \`\`
K\`
 \`
*\`
 \`
 \`
*\`
 \`
 \`
 \`
 \`
 \`
 \`
 \`\`
L\`
 \`
*\`
 \`
 \`
 \`
*\`
 \`
 \`
 \`
 \`
 \`
 \`\`
M\`
 \`
*\`
 \`
 \`
 \`
 \`
*\`
 \`
 \`
 \`
 \`
 \`\`
N\`
 \`
*\`
 \`
 \`
 \`
 \`
 \`
*\`
 \`
 \`
 \`
 \`\`
O\`
 \`
*\`
 \`
 \`
 \`
 \`
 \`
 \`
*\`
 \`
 \`
 \`\`
P\`
 \`
*\`
 \`
 \`
 \`
 \`
 \`
 \`
 \`
*\`
 \`
 \`\`
Q\`
 \`
*\`
 \`
 \`
 \`
 \`
 \`
 \`
 \`
 \`
*\`
 \`\`
R\`
 \`
*\`
 \`
 \`
 \`
 \`
 \`
 \`
 \`
 \`
 \`
*\`\`
S\`
 \`
 \`
*\`
 \`
*\`
 \`
 \`
 \`
 \`
 \`
 \`
 \`\`
T\`
 \`
 \`
*\`
 \`
 \`
*\`
 \`
 \`
 \`
 \`
 \`
 \`\`
U\`
 \`
 \`
*\`
 \`
 \`
 \`
*\`
 \`
 \`
 \`
 \`
 \`\`
V\`
 \`
 \`
*\`
 \`
 \`
 \`
 \`
*\`
 \`
 \`
 \`
 \`\`
W\`
 \`
 \`
*\`
 \`
 \`
 \`
 \`
 \`
*\`
 \`
 \`
 \`\`
X\`
 \`
 \`
*\`
 \`
 \`
 \`
 \`
 \`
 \`
*\`
 \`
 \`\`
Y\`
 \`
 \`
*\`
 \`
 \`
 \`
 \`
 \`
 \`
 \`
*\`
 \`\`
Z\`
 \`
 \`
*\`
 \`
 \`
 \`
 \`
 \`
 \`
 \`
 \`
*\`\`
[\`
 \`
*\`
 \`
 \`
*\`
 \`
 \`
 \`
 \`
 \`
*\`
 \`\`
\\\`
 \`
 \`
*\`
 \`
*\`
 \`
 \`
 \`
 \`
 \`
*\`
 \`\`
]\`
 \`
 \`
 \`
 \`
 \`
 \`
 \`
 \`
 \`
*\`
*\`
 \`\`
^\`
 \`
*\`
 \`
 \`
 \`
 \`
 \`
 \`
 \`
*\`
*\`
 \`\`
_\`
R\`
X\`
0\`
1\`
2\`
3\`
4\`
5\`
6\`
7\`
8\`
9\`\`
\`\`
 \`
 \`
 \`
 \`
 \`
 \`
 \`
 \`
 \`
 \`
 \`
 \`\`
a\`
*\`
 \`
 \`
*\`
 \`
 \`
 \`
 \`
 \`
 \`
 \`
 \`\`
b\`
*\`
 \`
 \`
 \`
*\`
 \`
 \`
 \`
 \`
 \`
 \`
 \`\`
c\`
*\`
 \`
 \`
 \`
 \`
*\`
 \`
 \`
 \`
 \`
 \`
 \`\`
d\`
*\`
 \`
 \`
 \`
 \`
 \`
*\`
 \`
 \`
 \`
 \`
 \`\`
e\`
*\`
 \`
 \`
 \`
 \`
 \`
 \`
*\`
 \`
 \`
 \`
 \`\`
f\`
*\`
 \`
 \`
 \`
 \`
 \`
 \`
 \`
*\`
 \`
 \`
 \`\`
g\`
*\`
 \`
 \`
 \`
 \`
 \`
 \`
 \`
 \`
*\`
 \`
 \`\`
h\`
*\`
 \`
 \`
 \`
 \`
 \`
 \`
 \`
 \`
 \`
*\`
 \`\`
i\`
*\`
 \`
 \`
 \`
 \`
 \`
 \`
 \`
 \`
 \`
 \`
*\`\`
j\`
 \`
*\`
 \`
*\`
 \`
 \`
 \`
 \`
 \`
 \`
 \`
 \`\`
k\`
 \`
*\`
 \`
 \`
*\`
 \`
 \`
 \`
 \`
 \`
 \`
 \`\`
l\`
 \`
*\`
 \`
 \`
 \`
*\`
 \`
 \`
 \`
 \`
 \`
 \`\`
m\`
 \`
*\`
 \`
 \`
 \`
 \`
*\`
 \`
 \`
 \`
 \`
 \`\`
n\`
 \`
*\`
 \`
 \`
 \`
 \`
 \`
*\`
 \`
 \`
 \`
 \`\`
o\`
 \`
*\`
 \`
 \`
 \`
 \`
 \`
 \`
*\`
 \`
 \`
 \`\`
p\`
 \`
*\`
 \`
 \`
 \`
 \`
 \`
 \`
 \`
*\`
 \`
 \`\`
q\`
 \`
*\`
 \`
 \`
 \`
 \`
 \`
 \`
 \`
 \`
*\`
 \`\`
r\`
 \`
*\`
 \`
 \`
 \`
 \`
 \`
 \`
 \`
 \`
 \`
*\`\`
s\`
 \`
 \`
*\`
 \`
*\`
 \`
 \`
 \`
 \`
 \`
 \`
 \`\`
t\`
 \`
 \`
*\`
 \`
 \`
*\`
 \`
 \`
 \`
 \`
 \`
 \`\`
u\`
 \`
 \`
*\`
 \`
 \`
 \`
*\`
 \`
 \`
 \`
 \`
 \`\`
v\`
 \`
 \`
*\`
 \`
 \`
 \`
 \`
*\`
 \`
 \`
 \`
 \`\`
w\`
 \`
 \`
*\`
 \`
 \`
 \`
 \`
 \`
*\`
 \`
 \`
 \`\`
x\`
 \`
 \`
*\`
 \`
 \`
 \`
 \`
 \`
 \`
*\`
 \`
 \`\`
y\`
 \`
 \`
*\`
 \`
 \`
 \`
 \`
 \`
 \`
 \`
*\`
 \`\`
z\`
 \`
 \`
*\`
 \`
 \`
 \`
 \`
 \`
 \`
 \`
 \`
*\`\`
{\`
 \`
 \`
 \`
 \`
 \`
 \`
 \`
 \`
 \`
 \`
 \`
 \`\`
|\`
 \`
 \`
*\`
 \`
 \`
 \`
 \`
 \`
 \`
*\`
*\`
 \`\`
}\`
 \`
 \`
 \`
 \`
 \`
 \`
 \`
 \`
 \`
 \`
 \`
 \`\`
~\`
 \`
 \`
 \`
 \`
 \`
 \`
 \`
 \`
 \`
 \`
 \`
 \`\`
A\`
*\`
 \`
 \`
*\`
 \`
 \`
 \`
 \`
 \`
 \`
 \`
 \`\`
O\`
 \`
*\`
 \`
 \`
 \`
 \`
 \`
 \`
*\`
 \`
 \`
 \`\`
U\`
 \`
 \`
*\`
 \`
 \`
 \`
*\`
 \`
 \`
 \`
 \`
 \`\`
a\`
*\`
 \`
 \`
*\`
 \`
 \`
 \`
 \`
 \`
 \`
 \`
 \`\`
o\`
 \`
*\`
 \`
 \`
 \`
 \`
 \`
 \`
*\`
 \`
 \`
 \`\`
u\`
 \`
 \`
*\`
 \`
 \`
 \`
*\`
 \`
 \`
 \`
 \`
 \`\`
�\`
 \`
 \`
 \`
 \`
 \`
 \`
 \`
 \`
 \`
 \`
 \`
 \`\`
`