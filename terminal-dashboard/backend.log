
> terminal-dashboard-backend@1.0.0 start
> node server.js


╔══════════════════════════════════════════════════════════════╗
║                 🚀 TERMINAL DASHBOARD SERVER                 ║
║                                                              ║
║  Server running on: http://localhost:3000                     ║
║  WebSocket ready for real-time terminal connections         ║
║                                                              ║
║  Features:                                                   ║
║  ✅ True terminal emulation with node-pty                   ║
║  ✅ Real-time WebSocket communication                        ║
║  ✅ Multiple concurrent programs                             ║
║  ✅ Interactive input/output                                 ║
║  ✅ Session management                                       ║
║                                                              ║
╚══════════════════════════════════════════════════════════════╝
  
