import express from 'express';
import { createServer } from 'http';
import { Server } from 'socket.io';
import pty from 'node-pty';
import cors from 'cors';
import fs from 'fs-extra';
import path from 'path';
import { fileURLToPath } from 'url';
import { v4 as uuidv4 } from 'uuid';
import chalk from 'chalk';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const app = express();
const server = createServer(app);
const io = new Server(server, {
  cors: {
    origin: "*",
    methods: ["GET", "POST"]
  }
});

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.static(path.join(__dirname, '../frontend/dist')));

// Store active terminals
const terminals = new Map();
const terminalSessions = new Map();

// Program configurations
const programs = [
  {
    id: '0g',
    name: '0G Network',
    description: 'Automated 0G Network transactions',
    directory: '0g',
    command: 'npm',
    args: ['start'],
    type: 'protocol',
    icon: '🌐',
    color: '#4F46E5'
  },
  {
    id: 'pharos',
    name: 'Pharos Protocol',
    description: 'Pharos protocol automation with console menu',
    directory: 'Pharos',
    command: 'node',
    args: ['main.js'],
    type: 'protocol',
    icon: '⚡',
    color: '#059669'
  },
  {
    id: 'maitrix',
    name: 'Maitrix Auto Bot',
    description: 'Maitrix automation with blessed UI',
    directory: 'Maitrix',
    command: 'npm',
    args: ['start'],
    type: 'bot',
    icon: '🤖',
    color: '#DC2626'
  },
  {
    id: 't1',
    name: 'T1 Auto Bridge',
    description: 'T1 bridge automation tool',
    directory: 'T1',
    command: 'npm',
    args: ['start'],
    type: 'bridge',
    icon: '🌉',
    color: '#7C3AED'
  },
  {
    id: 'rise',
    name: 'Rise Protocol',
    description: 'Rise protocol automation',
    directory: 'Rise',
    command: 'npm',
    args: ['start'],
    type: 'protocol',
    icon: '📈',
    color: '#EA580C'
  }
];

// API Routes
app.get('/api/programs', (req, res) => {
  res.json(programs);
});

app.get('/api/terminals', (req, res) => {
  const activeTerminals = Array.from(terminalSessions.values()).map(session => ({
    id: session.id,
    programId: session.programId,
    programName: session.programName,
    status: session.status,
    startTime: session.startTime,
    pid: session.terminal?.pid
  }));
  res.json(activeTerminals);
});

app.post('/api/programs/:id/start', async (req, res) => {
  const { id } = req.params;
  const program = programs.find(p => p.id === id);

  if (!program) {
    return res.status(404).json({ error: 'Program not found' });
  }

  try {
    const terminalId = uuidv4();
    const programPath = path.join(__dirname, '../../', program.directory);

    console.log(chalk.blue(`🚀 Starting ${program.name}...`));
    console.log(chalk.gray(`   Directory: ${programPath}`));
    console.log(chalk.gray(`   Command: ${program.command} ${program.args.join(' ')}`));

    // Check if directory exists
    if (!await fs.pathExists(programPath)) {
      throw new Error(`Program directory not found: ${programPath}`);
    }

    // Create PTY terminal
    const terminal = pty.spawn(program.command, program.args, {
      name: 'xterm-256color',
      cols: 120,
      rows: 30,
      cwd: programPath,
      env: {
        ...process.env,
        TERM: 'xterm-256color',
        COLORTERM: 'truecolor',
        FORCE_COLOR: '3',
        NODE_ENV: 'production'
      }
    });

    // Create session
    const session = {
      id: terminalId,
      programId: id,
      programName: program.name,
      terminal,
      status: 'running',
      startTime: new Date().toISOString(),
      history: []
    };

    terminals.set(terminalId, terminal);
    terminalSessions.set(terminalId, session);

    // Handle terminal data
    terminal.on('data', (data) => {
      session.history.push({
        type: 'output',
        data,
        timestamp: new Date().toISOString()
      });

      // Emit to all connected clients
      io.emit('terminal-data', {
        terminalId,
        programId: id,
        programName: program.name,
        data,
        timestamp: new Date().toISOString()
      });
    });

    // Handle terminal exit
    terminal.on('exit', (code, signal) => {
      console.log(chalk.yellow(`📋 ${program.name} exited with code ${code}`));

      session.status = 'stopped';
      session.exitCode = code;
      session.exitSignal = signal;
      session.endTime = new Date().toISOString();

      io.emit('terminal-exit', {
        terminalId,
        programId: id,
        programName: program.name,
        exitCode: code,
        exitSignal: signal,
        timestamp: new Date().toISOString()
      });

      // Clean up after 5 minutes
      setTimeout(() => {
        terminals.delete(terminalId);
        terminalSessions.delete(terminalId);
      }, 5 * 60 * 1000);
    });

    console.log(chalk.green(`✅ ${program.name} started with terminal ID: ${terminalId}`));

    res.json({
      success: true,
      terminalId,
      programId: id,
      programName: program.name,
      pid: terminal.pid
    });

  } catch (error) {
    console.error(chalk.red(`❌ Failed to start ${program.name}:`), error.message);
    res.status(500).json({ error: error.message });
  }
});

app.post('/api/terminals/:id/input', (req, res) => {
  const { id } = req.params;
  const { data } = req.body;

  const terminal = terminals.get(id);
  const session = terminalSessions.get(id);

  if (!terminal || !session) {
    return res.status(404).json({ error: 'Terminal not found' });
  }

  try {
    terminal.write(data);

    session.history.push({
      type: 'input',
      data,
      timestamp: new Date().toISOString()
    });

    console.log(chalk.cyan(`📝 Input sent to ${session.programName}: "${data.replace(/\r?\n/g, '\\n')}"`));

    res.json({ success: true });
  } catch (error) {
    console.error(chalk.red(`❌ Failed to send input to terminal ${id}:`), error.message);
    res.status(500).json({ error: error.message });
  }
});

app.post('/api/terminals/:id/stop', (req, res) => {
  const { id } = req.params;
  const terminal = terminals.get(id);
  const session = terminalSessions.get(id);

  if (!terminal || !session) {
    return res.status(404).json({ error: 'Terminal not found' });
  }

  try {
    terminal.kill();
    session.status = 'stopped';

    console.log(chalk.yellow(`🛑 Stopped ${session.programName}`));

    res.json({ success: true });
  } catch (error) {
    console.error(chalk.red(`❌ Failed to stop terminal ${id}:`), error.message);
    res.status(500).json({ error: error.message });
  }
});

// Socket.IO connection handling
io.on('connection', (socket) => {
  console.log(chalk.green(`🔌 Client connected: ${socket.id}`));

  // Send current active terminals to new client
  const activeTerminals = Array.from(terminalSessions.values()).map(session => ({
    id: session.id,
    programId: session.programId,
    programName: session.programName,
    status: session.status,
    startTime: session.startTime
  }));

  socket.emit('active-terminals', activeTerminals);

  // Handle terminal input from client
  socket.on('terminal-input', (data) => {
    const { terminalId, input } = data;
    const terminal = terminals.get(terminalId);

    if (terminal) {
      terminal.write(input);
    }
  });

  // Handle terminal resize
  socket.on('terminal-resize', (data) => {
    const { terminalId, cols, rows } = data;
    const terminal = terminals.get(terminalId);

    if (terminal) {
      terminal.resize(cols, rows);
    }
  });

  socket.on('disconnect', () => {
    console.log(chalk.gray(`🔌 Client disconnected: ${socket.id}`));
  });
});

// Start server
const PORT = process.env.PORT || 3001;
server.listen(PORT, () => {
  console.log(chalk.bold.blue(`
╔══════════════════════════════════════════════════════════════╗
║                 🚀 TERMINAL DASHBOARD SERVER                 ║
║                                                              ║
║  Server running on: http://localhost:${PORT}                     ║
║  WebSocket ready for real-time terminal connections         ║
║                                                              ║
║  Features:                                                   ║
║  ✅ True terminal emulation with node-pty                   ║
║  ✅ Real-time WebSocket communication                        ║
║  ✅ Multiple concurrent programs                             ║
║  ✅ Interactive input/output                                 ║
║  ✅ Session management                                       ║
║                                                              ║
╚══════════════════════════════════════════════════════════════╝
  `));
});

export default app;
