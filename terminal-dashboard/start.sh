#!/bin/bash

echo "🚀 Starting Terminal Dashboard System..."
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    print_error "Node.js is not installed. Please install Node.js first."
    exit 1
fi

# Check if npm is installed
if ! command -v npm &> /dev/null; then
    print_error "npm is not installed. Please install npm first."
    exit 1
fi

print_status "Node.js version: $(node --version)"
print_status "npm version: $(npm --version)"
echo ""

# Start backend server
print_status "Starting backend server..."
cd backend
if [ ! -d "node_modules" ]; then
    print_warning "Installing backend dependencies..."
    npm install
fi

# Start backend in background
nohup npm start > ../backend.log 2>&1 &
BACKEND_PID=$!
echo $BACKEND_PID > ../backend.pid

print_success "Backend server started with PID: $BACKEND_PID"
echo ""

# Wait a moment for backend to start
sleep 3

# Start frontend development server
print_status "Starting frontend development server..."
cd ../frontend

if [ ! -d "node_modules" ]; then
    print_warning "Installing frontend dependencies..."
    npm install
fi

# Build frontend for production
print_status "Building frontend for production..."
npm run build

print_success "Frontend built successfully!"
echo ""

# Check if backend is running
sleep 2
if ps -p $BACKEND_PID > /dev/null; then
    print_success "✅ Terminal Dashboard is running!"
    echo ""
    echo "🌐 Access the dashboard at: http://localhost:3000"
    echo "📊 Backend API available at: http://localhost:3000/api"
    echo "🔌 WebSocket connection: ws://localhost:3000"
    echo ""
    echo "📋 Available endpoints:"
    echo "   • GET  /api/programs        - List all programs"
    echo "   • POST /api/programs/:id/start - Start a program"
    echo "   • POST /api/terminals/:id/input - Send input to terminal"
    echo "   • POST /api/terminals/:id/stop  - Stop a terminal"
    echo ""
    echo "🛑 To stop the system, run: ./stop.sh"
    echo "📝 Backend logs: tail -f backend.log"
    echo ""
    print_warning "Press Ctrl+C to stop the system or run ./stop.sh"
    
    # Keep script running to show logs
    tail -f ../backend.log
else
    print_error "Failed to start backend server"
    exit 1
fi
