#!/bin/bash

echo "🛑 Stopping Terminal Dashboard System..."
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Stop backend server
if [ -f "backend.pid" ]; then
    BACKEND_PID=$(cat backend.pid)
    print_status "Stopping backend server (PID: $BACKEND_PID)..."
    
    if ps -p $BACKEND_PID > /dev/null; then
        kill $BACKEND_PID
        sleep 2
        
        # Force kill if still running
        if ps -p $BACKEND_PID > /dev/null; then
            print_status "Force killing backend server..."
            kill -9 $BACKEND_PID
        fi
        
        print_success "Backend server stopped"
    else
        print_error "Backend server was not running"
    fi
    
    rm -f backend.pid
else
    print_error "No backend PID file found"
fi

# Kill any remaining node processes related to terminal-dashboard
print_status "Cleaning up any remaining processes..."
pkill -f "terminal-dashboard" 2>/dev/null || true
pkill -f "node.*server.js" 2>/dev/null || true

# Clean up log files
if [ -f "backend.log" ]; then
    print_status "Archiving backend log..."
    mv backend.log "backend-$(date +%Y%m%d-%H%M%S).log"
fi

print_success "✅ Terminal Dashboard system stopped successfully!"
echo ""
print_status "To start the system again, run: ./start.sh"
