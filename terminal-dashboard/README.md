# 🚀 Terminal Dashboard - Modern Web-based CLI Manager

A modern, real-time web-based terminal dashboard for managing multiple CLI programs simultaneously. Built with cutting-edge technologies for the best user experience.

## ✨ Features

### 🎯 **Core Features**
- **True Terminal Emulation** with xterm.js - 100% compatible with CLI programs
- **Real-time Communication** via WebSocket for instant updates
- **Multiple Concurrent Programs** - run many programs simultaneously
- **Interactive Input/Output** - full keyboard and mouse support
- **Session Management** - persistent terminal sessions
- **Modern UI/UX** with React + Tailwind CSS

### 🔧 **Advanced Features**
- **Program Management** - start, stop, monitor programs with one click
- **Terminal Tabs** - switch between multiple running programs
- **Full-screen Mode** - immersive terminal experience
- **Search & Copy** - search terminal content and copy output
- **Log Export** - download terminal logs for debugging
- **Resource Monitoring** - track CPU, memory, uptime
- **Auto-reconnect** - resilient WebSocket connections

### 🎨 **User Experience**
- **Responsive Design** - works on desktop, tablet, mobile
- **Dark Theme** - easy on the eyes for long sessions
- **Keyboard Shortcuts** - efficient navigation
- **Visual Feedback** - real-time status indicators
- **Error Handling** - graceful error recovery

## 🏗️ **Architecture**

```
┌─────────────────┐    WebSocket    ┌─────────────────┐    node-pty    ┌─────────────────┐
│   React Frontend │ ◄──────────────► │  Node.js Server │ ◄─────────────► │   CLI Programs  │
│                 │                 │                 │                │                 │
│ • xterm.js      │                 │ • Express       │                │ • 0G Network    │
│ • Socket.io     │                 │ • Socket.io     │                │ • Pharos        │
│ • Tailwind CSS  │                 │ • node-pty      │                │ • Maitrix       │
│ • Lucide Icons  │                 │ • fs-extra      │                │ • T1 Bridge     │
└─────────────────┘                 └─────────────────┘                └─────────────────┘
```

## 🚀 **Quick Start**

### Prerequisites
- **Node.js** v18+ 
- **npm** v8+
- **Linux/macOS** (Windows with WSL)

### Installation & Setup

1. **Clone or navigate to the terminal-dashboard directory**
```bash
cd terminal-dashboard
```

2. **Make scripts executable**
```bash
chmod +x start.sh stop.sh
```

3. **Start the system**
```bash
./start.sh
```

4. **Access the dashboard**
```
🌐 Open: http://localhost:3000
```

### Stopping the System
```bash
./stop.sh
```

## 📖 **Usage Guide**

### 🎮 **Dashboard View**
- **Program Grid**: See all available programs with status indicators
- **Quick Stats**: Monitor active terminals and running programs
- **One-click Start**: Click "Start Program" to launch any CLI tool
- **Real-time Status**: See which programs are running, stopped, or starting

### 💻 **Terminal View**
- **Multi-tab Interface**: Switch between multiple running programs
- **Full Terminal Emulation**: 100% compatible with CLI programs
- **Interactive Input**: Type commands, navigate menus, use arrow keys
- **Real-time Output**: See program output instantly
- **Terminal Controls**: Search, copy, download logs, fullscreen mode

### ⌨️ **Keyboard Shortcuts**
- **Ctrl+C**: Send interrupt signal to program
- **Ctrl+D**: Send EOF signal
- **Arrow Keys**: Navigate blessed UI programs
- **Tab**: Auto-complete (if supported by program)
- **Ctrl+Shift+F**: Search in terminal
- **F11**: Toggle fullscreen mode

## 🔧 **Configuration**

### Adding New Programs

Edit `backend/server.js` to add new programs:

```javascript
const programs = [
  {
    id: 'my-program',
    name: 'My Program',
    description: 'Description of my program',
    directory: 'my-program-folder',
    command: 'npm',
    args: ['start'],
    type: 'bot',
    icon: '🤖',
    color: '#4F46E5'
  }
];
```

### Program Types
- **protocol**: Blockchain protocols (0G, Pharos)
- **bot**: Automation bots (Maitrix)
- **bridge**: Bridge tools (T1)
- **defi**: DeFi tools
- **nft**: NFT tools

## 🛠️ **Development**

### Project Structure
```
terminal-dashboard/
├── backend/                 # Node.js server
│   ├── server.js           # Main server file
│   ├── package.json        # Backend dependencies
│   └── node_modules/       # Backend packages
├── frontend/               # React frontend
│   ├── src/
│   │   ├── components/     # React components
│   │   ├── App.jsx         # Main app component
│   │   ├── main.jsx        # Entry point
│   │   └── index.css       # Global styles
│   ├── index.html          # HTML template
│   ├── package.json        # Frontend dependencies
│   └── dist/               # Built frontend
├── start.sh                # Start script
├── stop.sh                 # Stop script
└── README.md               # This file
```

### Development Mode

**Backend Development:**
```bash
cd backend
npm run dev  # Uses nodemon for auto-restart
```

**Frontend Development:**
```bash
cd frontend
npm run dev  # Vite dev server with hot reload
```

### Building for Production
```bash
cd frontend
npm run build
```

## 🔌 **API Reference**

### REST Endpoints

| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/api/programs` | List all available programs |
| GET | `/api/terminals` | List active terminal sessions |
| POST | `/api/programs/:id/start` | Start a program |
| POST | `/api/terminals/:id/input` | Send input to terminal |
| POST | `/api/terminals/:id/stop` | Stop a terminal |

### WebSocket Events

| Event | Direction | Description |
|-------|-----------|-------------|
| `terminal-data` | Server → Client | Real-time terminal output |
| `terminal-exit` | Server → Client | Program exit notification |
| `terminal-input` | Client → Server | Send input to terminal |
| `terminal-resize` | Client → Server | Resize terminal |
| `active-terminals` | Server → Client | List of active terminals |

## 🧪 **Testing**

### Manual Testing
1. Start the system: `./start.sh`
2. Open browser: `http://localhost:3000`
3. Start a program from the dashboard
4. Test terminal interaction
5. Verify real-time output
6. Test multiple programs simultaneously

### API Testing
```bash
# List programs
curl http://localhost:3000/api/programs

# Start a program
curl -X POST http://localhost:3000/api/programs/0g/start

# Send input to terminal
curl -X POST http://localhost:3000/api/terminals/TERMINAL_ID/input \
  -H "Content-Type: application/json" \
  -d '{"data":"10\n"}'
```

## 🚨 **Troubleshooting**

### Common Issues

**1. Port 3000 already in use**
```bash
# Kill process using port 3000
sudo lsof -ti:3000 | xargs kill -9
```

**2. Permission denied on scripts**
```bash
chmod +x start.sh stop.sh
```

**3. Node.js version issues**
```bash
# Check Node.js version
node --version  # Should be v18+
```

**4. Dependencies not installed**
```bash
# Reinstall dependencies
cd backend && rm -rf node_modules && npm install
cd ../frontend && rm -rf node_modules && npm install
```

### Debug Mode
```bash
# Enable debug logging
DEBUG=* ./start.sh

# View backend logs
tail -f backend.log

# View browser console for frontend logs
```

## 📊 **Performance**

### System Requirements
- **RAM**: 512MB minimum, 1GB recommended
- **CPU**: 1 core minimum, 2+ cores recommended
- **Storage**: 100MB for system, varies by programs
- **Network**: Local network for WebSocket communication

### Optimization Tips
- **Limit concurrent programs** to avoid resource exhaustion
- **Monitor terminal history** - large outputs can consume memory
- **Use fullscreen mode** for better performance
- **Close unused terminals** to free resources

## 🔒 **Security**

### Security Features
- **Local-only access** by default (localhost:3000)
- **No external network access** required
- **Process isolation** via node-pty
- **Input validation** on all API endpoints
- **XSS protection** via React and proper escaping

### Security Considerations
- **Run in trusted environment** only
- **Don't expose to public internet** without authentication
- **Monitor resource usage** to prevent DoS
- **Keep dependencies updated** for security patches

## 🤝 **Contributing**

### Development Setup
1. Fork the repository
2. Create feature branch: `git checkout -b feature/amazing-feature`
3. Make changes and test thoroughly
4. Commit changes: `git commit -m 'Add amazing feature'`
5. Push to branch: `git push origin feature/amazing-feature`
6. Open a Pull Request

### Code Style
- **ESLint** for JavaScript linting
- **Prettier** for code formatting
- **Conventional Commits** for commit messages
- **JSDoc** for function documentation

## 📄 **License**

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 **Acknowledgments**

- **xterm.js** - Amazing terminal emulator
- **Socket.io** - Real-time communication
- **React** - Modern UI framework
- **Tailwind CSS** - Utility-first CSS
- **node-pty** - True terminal emulation
- **Lucide** - Beautiful icons

---

**Made with ❤️ for the testnet community**

🚀 **Happy Testing!** 🚀
