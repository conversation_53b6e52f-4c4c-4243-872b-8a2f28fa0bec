# 🚀 Testnet Program Dashboard

Dashboard web yang powerful untuk mengelola dan menjalankan semua program testnet blockchain Anda dari satu tempat. Mendukung 25+ program testnet dengan fitur interaktif lengkap.

## ✨ Features

### 🖥️ **Dashboard Interface**
- **Grid Layout**: Menampilkan semua program dalam grid yang rapi
- **Real-time Status**: Status running/stopped untuk setiap program
- **Filter & Search**: Filter berdasarkan tipe program dan search function
- **Responsive Design**: Bekerja di desktop dan mobile

### ⚡ **Program Management**
- **Auto Dependency Installation**: Otomatis install npm dependencies
- **Build Process**: Auto build untuk TypeScript projects
- **File Validation**: Cek file yang diperlukan sebelum start
- **Multiple Programs**: Jalankan banyak program bersamaan
- **Smart Command Detection**: Gunakan npm start atau node sesuai konfigurasi

### 💻 **Interactive Console**
- **Real-time Output**: Live output dari program dengan color-coding
- **Interactive Input**: Kirim command ke program yang berjalan
- **Arrow Key Support**: Tombol panah untuk navigasi CLI
- **Special Keys**: Enter, Escape, dan control keys
- **ANSI Color Support**: Tampilan warna yang sesuai dengan terminal asli
- **Console History**: Setiap program memiliki console history terpisah

### 📁 **File Management**
- **File Browser**: Lihat semua file dalam direktori program
- **Code Editor**: Edit file langsung di browser
- **Template Creation**: Buat file template dengan satu klik
- **Auto-save**: Simpan perubahan file

## 🛠️ Installation & Setup

### Prerequisites
- **Node.js** (v14 atau lebih baru)
- **npm** atau **yarn**
- **Git** (untuk clone repository)

### 1. Clone Repository
```bash
git clone <repository-url>
cd testnet-program-dashboard
```

### 2. Install Dependencies
```bash
cd web-dashboard
npm install
```

### 3. Setup Program Directories
Pastikan semua program testnet Anda berada di direktori yang benar:
```
testnet-program-dashboard/
├── web-dashboard/          # Dashboard files
├── 0g/                    # 0G Network program
├── Rise/                  # Rise Protocol program
├── merak/                 # Merak SUI DEX Bot
├── Union/                 # Union Auto Bot
├── kite/                  # Kite AI Auto Bot
├── TradeGPT/             # TradeGPT Auto Bot
└── ... (other programs)
```

### 4. Start Dashboard
```bash
npm start
```

### 5. Open Browser
Navigate to: `http://localhost:3000`

## 🎯 Supported Programs (25 Programs)

### Blockchain & DeFi (6)
1. **0G Network** - Auto swap 0g-testnet (with build process)
2. **Merak SUI DEX Bot** - SUI DEX automation
3. **Inco Network** - INCO Auto Bot
4. **Maitrix Network** - Maitrix automation
5. **Monad Testnet** - Monad automation
6. **Enso Finance** - Enso automation

### Protocols (10)
7. **Byte Protocol** - Byte automation
8. **Euclid Bot** - Euclid automation
9. **Kite AI Auto Bot** - NT EXHAUST KITE
10. **Pharos Protocol** - Pharos automation
11. **R2 Protocol** - R2 automation
12. **Rise Protocol** - Rise Testnet Bot
13. **Shift Protocol** - Shift automation
14. **T1 Auto Bridge** - SOUIY T1 Bridge
15. **Union Auto Bot** - Union Testnet Bot
16. **Rome EVM Deployer** - EVM deployment

### Bots & Trading (4)
17. **Aster AutoBot NTE** - Aster network bot
18. **TradeGPT Auto Bot** - TradeGPT Finance bot
19. **INFTS Protocol** - INFTS automation
20. **Coinsif Referral** - Coinsif system

### Storage & Testing (5)
21. **0G Storage** - Zero Gravity storage
22. **Huddle Protocol** - Huddle deployment
23. **Faucet Test** - Automated faucet testing
24. **R2 NTE** - R2 NTE automation
25. **Pharos Original** - Original implementation

## 📖 How to Use

### 1. **Dashboard Overview**
- Lihat semua program dalam grid layout
- Status real-time (hijau = running, merah = stopped)
- Filter berdasarkan tipe atau search berdasarkan nama

### 2. **Quick Start/Stop**
- Klik tombol **"Quick Start"** (hijau) untuk menjalankan program
- Klik tombol **"Stop"** (merah) untuk menghentikan program

### 3. **Detailed Management**
- Klik **"Manage"** untuk membuka detail program
- Gunakan tab **Console**, **Files**, dan **Config**

### 4. **Console Tab**
- **Start/Stop**: Kontrol program
- **Real-time Output**: Lihat output program secara live
- **Interactive Input**: 
  - Ketik pesan di input field dan tekan Enter
  - Gunakan arrow keys untuk navigasi CLI
  - Tombol Enter, Escape untuk control
- **Clear**: Bersihkan console output

### 5. **Files Tab**
- Browse semua file dalam direktori program
- Klik file untuk membuka di editor

### 6. **Config Tab**
- Edit file konfigurasi langsung di browser
- **Create Template**: Buat file template (.env, pvkey.txt, dll)
- **Save**: Simpan perubahan

## 🔧 Configuration Files

### Required Files for Programs

#### **Environment Files (.env)**
```env
# Private Keys (pisahkan dengan koma)
PRIVATE_KEYS=key1,key2,key3

# RPC URLs
RPC_URL=https://rpc-url-here
TESTNET_RPC=https://testnet-rpc-here

# API Keys
API_KEY=your_api_key_here

# Delays
DELAY_MIN=1000
DELAY_MAX=5000
```

#### **Private Key Files (pvkey.txt, accounts.txt)**
```
# Add your private keys here, one per line
******************************************
******************************************
```

#### **Wallet Configuration (wallet.json)**
```json
{
  "wallets": [
    {
      "privateKey": "0x...",
      "address": "0x..."
    }
  ],
  "config": {
    "delayMin": 1000,
    "delayMax": 5000
  }
}
```

## 🎮 Interactive Features

### **Arrow Key Navigation**
- **↑ ↓ ← →**: Navigasi dalam CLI program
- **Enter**: Konfirmasi pilihan
- **Escape**: Keluar dari menu

### **Special Commands**
- **Ctrl+C**: Stop program (gunakan Stop button)
- **Clear**: Bersihkan console
- **Input History**: Setiap program memiliki history terpisah

### **Multi-Program Support**
- Jalankan multiple program bersamaan
- Setiap program memiliki console terpisah
- Switch antar program tanpa kehilangan output

## 🔍 Troubleshooting

### **Program Tidak Bisa Start**
1. **Cek Dependencies**: Dashboard akan auto-install
2. **Cek File Konfigurasi**: Pastikan .env, pvkey.txt ada
3. **Cek Console Output**: Lihat error message

### **Console Tercampur**
- ✅ **FIXED**: Setiap program sekarang memiliki console terpisah
- Switch antar program untuk melihat output masing-masing

### **Input Tidak Berfungsi**
1. Pastikan program sedang running (status hijau)
2. Coba gunakan arrow keys untuk navigasi
3. Restart program jika perlu

### **Port Already in Use**
```bash
# Kill process yang menggunakan port 3000
lsof -ti:3000 | xargs kill -9

# Atau gunakan port lain
PORT=8080 npm start
```

## 🚀 Advanced Usage

### **Custom Port**
```bash
PORT=8080 npm start
```

### **Development Mode**
```bash
npm run dev
```

### **Add New Program**
Edit `server.js` dan tambahkan program baru:
```javascript
{
  id: 'new-program',
  name: 'New Program',
  description: 'Description here',
  directory: 'new-program',
  command: 'npm',
  args: ['start'],
  type: 'protocol',
  hasPackageJson: true,
  isInteractive: true,
  requiresFiles: ['.env']
}
```

## 📊 API Endpoints

- `GET /api/programs` - List all programs
- `GET /api/running-programs` - List running programs
- `POST /api/programs/:id/start` - Start program
- `POST /api/programs/:id/stop` - Stop program
- `POST /api/programs/:id/input` - Send input to program
- `GET /api/programs/:id/status` - Get program status

## 🤝 Contributing

1. Fork repository
2. Create feature branch
3. Make changes
4. Test thoroughly
5. Submit pull request

## 📞 Support

Jika mengalami masalah:
1. Cek console browser (F12) untuk JavaScript errors
2. Cek terminal server untuk backend errors
3. Restart dashboard jika diperlukan

---

## 🎉 **DASHBOARD READY TO USE!**

**Website testnet program dashboard sudah siap mengelola semua 25 program testnet Anda dengan fitur yang lengkap dan interaktif! 🚀**

**Happy Testing! 🎊**
