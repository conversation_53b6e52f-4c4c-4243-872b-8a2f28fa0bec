{"version": 3, "file": "Browser.js", "sourceRoot": "", "sources": ["../../../../src/bidi/Browser.ts"], "names": [], "mappings": ";AAAA;;;;GAIG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOH,kDAM2B;AAM3B,+DAAuD;AACvD,+CAA6C;AAE7C,yDAA6C;AAE7C,2DAAuD;AAGvD,kDAA0C;AAE1C,2CAA8C;AAe9C;;GAEG;IACU,WAAW;sBAAS,oBAAO;;;;;iBAA3B,WAAY,SAAQ,WAAO;;;kDAmDrC,IAAA,sBAAM,GAAE;YACT,0DAAA,uBAAA,6DAA6D,2BAAA,EAA7D,uBAAA,mEAA6D,2BAAA,yIAApD,eAAe,yBAAf,eAAe,6BAAf,eAAe,uHAAqC;;;QAnDpD,QAAQ,GAAG,eAAe,CAAC;QAEpC,MAAM,CAAU,gBAAgB,GAA0B;YACxD,iBAAiB;YACjB,SAAS;YACT,KAAK;YACL,QAAQ;SACT,CAAC;QACF,MAAM,CAAU,kBAAkB,GAA0B;YAC1D,WAAW;YACX,gCAAgC;YAChC,8BAA8B;YAC9B,2CAA2C;YAC3C,UAAU;YACV,kCAAkC;YAClC,mDAAmD;YACnD,oCAAoC;YACpC,gCAAgC;YAChC,+BAA+B;SAChC,CAAC;QAEF,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,IAAwB;YAC1C,MAAM,OAAO,GAAG,MAAM,oBAAO,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;gBAClD,UAAU,EAAE,IAAI,CAAC,YAAY,EAAE,UAAU;gBACzC,WAAW,EAAE;oBACX,GAAG,IAAI,CAAC,YAAY,EAAE,WAAW;oBACjC,+DAA+D;oBAC/D,mBAAmB,EAAE,IAAI,CAAC,mBAAmB;oBAC7C,uBAAuB,EAAE;wBACvB,OAAO,0DAA2C;qBACnD;oBACD,YAAY,EAAE,IAAI;oBAClB,8DAA8D;oBAC9D,4DAA4D;oBAC5D,oDAAoD;oBACpD,2BAA2B,EAAE,IAAI;iBAClC;aACF,CAAC,CAAC;YAEH,MAAM,OAAO,CAAC,SAAS,CACrB,OAAO,CAAC,YAAY,CAAC,WAAW,CAAC,iBAAiB,EAAE,CAAC,QAAQ,CAAC,SAAS,CAAC;gBACtE,CAAC,CAAC,WAAW,CAAC,gBAAgB;gBAC9B,CAAC,CAAC,CAAC,GAAG,WAAW,CAAC,gBAAgB,EAAE,GAAG,WAAW,CAAC,kBAAkB,CAAC,CACzE,CAAC;YAEF,MAAM,OAAO,GAAG,IAAI,WAAW,CAAC,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;YACvD,OAAO,CAAC,WAAW,EAAE,CAAC;YACtB,OAAO,OAAO,CAAC;QACjB,CAAC;QAGQ,gCAAe,iEAAG,IAAI,8BAAY,EAAiB,EAAC;QAD7D,IACS,eAAe,gEAAqC;QAD7D,IACS,eAAe,4EAAqC;QAE7D,QAAQ,sEAAgB;QACxB,cAAc,CAAwB;QACtC,YAAY,CAAc;QAC1B,gBAAgB,CAAkB;QAClC,gBAAgB,GAAG,IAAI,OAAO,EAAmC,CAAC;QAClE,OAAO,GAAG,IAAI,6BAAiB,CAAC,IAAI,CAAC,CAAC;QACtC,cAAc,CAAiB;QAE/B,YAAoB,WAAwB,EAAE,IAAwB;YACpE,KAAK,EAAE,CAAC;YACR,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC;YAC7B,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,aAAa,CAAC;YACzC,IAAI,CAAC,YAAY,GAAG,WAAW,CAAC;YAChC,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,eAAe,CAAC;YAC7C,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,aAAa,CAAC;QAC3C,CAAC;QAED,WAAW;YACT,kCAAkC;YAClC,KAAK,MAAM,WAAW,IAAI,IAAI,CAAC,YAAY,CAAC,YAAY,EAAE,CAAC;gBACzD,IAAI,CAAC,qBAAqB,CAAC,WAAW,CAAC,CAAC;YAC1C,CAAC;YAED,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,cAAc,EAAE,GAAG,EAAE;gBAC1C,IAAI,CAAC,eAAe,CAAC,IAAI,iDAA4B,SAAS,CAAC,CAAC;gBAChE,IAAI,CAAC,eAAe,CAAC,kBAAkB,EAAE,CAAC;YAC5C,CAAC,CAAC,CAAC;YACH,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,OAAO,EAAE,GAAG,EAAE;gBAChC,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,yBAAyB,EAAE,IAAI,CAAC,CAAC;gBAC3D,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;YAC5B,CAAC,CAAC,CAAC;QACL,CAAC;QAED,IAAI,YAAY;YACd,OAAO,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,YAAY,CAAC,WAAW,CAAC;QAC5D,CAAC;QACD,IAAI,eAAe;YACjB,OAAO,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,YAAY,CAAC,cAAc,CAAC;QAC/D,CAAC;QAED,IAAI,YAAY;YACd,OAAO,IAAI,CAAC,cAAc,KAAK,SAAS,CAAC;QAC3C,CAAC;QAED,IAAI,aAAa;YACf,OAAO,IAAI,CAAC,cAAc,CAAC;QAC7B,CAAC;QAEQ,KAAK,CAAC,SAAS;YACtB,OAAO,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,YAAY,CAAC,SAAS,CAAC;QAC1D,CAAC;QAED,qBAAqB,CAAC,WAAwB;YAC5C,MAAM,cAAc,GAAG,sCAAkB,CAAC,IAAI,CAAC,IAAI,EAAE,WAAW,EAAE;gBAChE,eAAe,EAAE,IAAI,CAAC,gBAAgB;aACvC,CAAC,CAAC;YACH,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,WAAW,EAAE,cAAc,CAAC,CAAC;YAEvD,cAAc,CAAC,cAAc,CAAC,EAAE,0DAE9B,MAAM,CAAC,EAAE;gBACP,IAAI,CAAC,eAAe,CAAC,IAAI,mDAA6B,MAAM,CAAC,CAAC;YAChE,CAAC,CACF,CAAC;YACF,cAAc,CAAC,cAAc,CAAC,EAAE,0DAE9B,MAAM,CAAC,EAAE;gBACP,IAAI,CAAC,eAAe,CAAC,IAAI,mDAA6B,MAAM,CAAC,CAAC;YAChE,CAAC,CACF,CAAC;YACF,cAAc,CAAC,cAAc,CAAC,EAAE,8DAE9B,MAAM,CAAC,EAAE;gBACP,IAAI,CAAC,eAAe,CAAC,IAAI,uDAA+B,MAAM,CAAC,CAAC;YAClE,CAAC,CACF,CAAC;YAEF,OAAO,cAAc,CAAC;QACxB,CAAC;QAED,IAAI,UAAU;YACZ,2CAA2C;YAC3C,OAAO,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,UAA4B,CAAC;QAChE,CAAC;QAEQ,UAAU;YACjB,OAAO,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC;QAC7B,CAAC;QAEQ,KAAK,CAAC,KAAK;YAClB,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC;gBAC3B,OAAO;YACT,CAAC;YAED,IAAI,CAAC;gBACH,MAAM,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,CAAC;gBAChC,MAAM,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;YACxC,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,iBAAiB;gBACjB,IAAA,oBAAU,EAAC,KAAK,CAAC,CAAC;YACpB,CAAC;oBAAS,CAAC;gBACT,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;YAC5B,CAAC;QACH,CAAC;QAED,IAAa,SAAS;YACpB,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC;QACzC,CAAC;QAEQ,OAAO;YACd,OAAO,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC;QAC/B,CAAC;QAEQ,KAAK,CAAC,oBAAoB,CACjC,QAAgC;YAEhC,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,iBAAiB,EAAE,CAAC;YAChE,OAAO,IAAI,CAAC,qBAAqB,CAAC,WAAW,CAAC,CAAC;QACjD,CAAC;QAEQ,KAAK,CAAC,OAAO;YACpB,OAAO,GAAG,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;QACxD,CAAC;QAEQ,eAAe;YACtB,OAAO,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE;gBACvD,OAAO,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,OAAO,CAAE,CAAC;YAC7C,CAAC,CAAC,CAAC;QACL,CAAC;QAEQ,qBAAqB;YAC5B,OAAO,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,IAAI,CAAC,YAAY,CAAC,kBAAkB,CAAE,CAAC;QAC1E,CAAC;QAEQ,OAAO;YACd,OAAO,IAAI,CAAC,qBAAqB,EAAE,CAAC,OAAO,EAAE,CAAC;QAChD,CAAC;QAEQ,OAAO;YACd,OAAO;gBACL,IAAI,CAAC,OAAO;gBACZ,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;oBAC1C,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;gBAC3B,CAAC,CAAC;aACH,CAAC;QACJ,CAAC;QAEQ,MAAM;YACb,OAAO,IAAI,CAAC,OAAO,CAAC;QACtB,CAAC;QAEQ,KAAK,CAAC,UAAU;YACvB,IAAI,CAAC;gBACH,MAAM,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC;YACxC,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,iBAAiB;gBACjB,IAAA,oBAAU,EAAC,KAAK,CAAC,CAAC;YACpB,CAAC;oBAAS,CAAC;gBACT,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;YAC5B,CAAC;QACH,CAAC;QAED,IAAa,SAAS;YACpB,OAAO;gBACL,qBAAqB,EAAE,IAAI,CAAC,UAAU,CAAC,wBAAwB,EAAE;aAClE,CAAC;QACJ,CAAC;;;AA5NU,kCAAW"}