{"version": 3, "file": "Frame.d.ts", "sourceRoot": "", "sources": ["../../../../src/bidi/Frame.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,KAAK,IAAI,MAAM,4CAA4C,CAAC;AAgBnE,OAAO,KAAK,EAAC,UAAU,EAAC,MAAM,sBAAsB,CAAC;AACrD,OAAO,EACL,KAAK,EAEL,KAAK,WAAW,EAChB,KAAK,cAAc,EACpB,MAAM,iBAAiB,CAAC;AAEzB,OAAO,EAAC,aAAa,EAAC,MAAM,yBAAyB,CAAC;AAOtD,OAAO,KAAK,EAAC,eAAe,EAAC,MAAM,8BAA8B,CAAC;AAClE,OAAO,KAAK,EAAC,SAAS,EAAC,MAAM,oBAAoB,CAAC;AASlD,OAAO,EAAC,cAAc,EAAC,MAAM,iBAAiB,CAAC;AAC/C,OAAO,KAAK,EAAC,eAAe,EAAC,MAAM,2BAA2B,CAAC;AAK/D,OAAO,KAAK,EAAC,iBAAiB,EAAC,MAAM,oBAAoB,CAAC;AAG1D,OAAO,KAAK,EAAC,gBAAgB,EAAC,MAAM,mBAAmB,CAAC;AAExD,OAAO,KAAK,EAAC,QAAQ,EAAC,MAAM,WAAW,CAAC;AACxC,OAAO,KAAK,EAAC,SAAS,EAAC,MAAM,YAAY,CAAC;AAC1C,OAAO,EAAC,cAAc,EAAC,MAAM,YAAY,CAAC;AAmB1C,qBAAa,SAAU,SAAQ,KAAK;;IAClC,MAAM,CAAC,IAAI,CACT,MAAM,EAAE,QAAQ,GAAG,SAAS,EAC5B,eAAe,EAAE,eAAe,GAC/B,SAAS;IAOZ,QAAQ,CAAC,eAAe,EAAE,eAAe,CAAC;IAE1C,QAAQ,CAAC,MAAM,EAAE;QAAC,OAAO,EAAE,cAAc,CAAC;QAAC,QAAQ,EAAE,cAAc,CAAA;KAAC,CAAC;IAErE,SAAkB,GAAG,EAAE,MAAM,CAAC;IAC9B,SAAkB,MAAM,EAAE,cAAc,CAAC;IACzC,SAAkB,aAAa,EAAE,aAAa,CAAC;IAE/C,OAAO;IAwJP,IAAI,eAAe,IAAI,eAAe,CAErC;IAEQ,SAAS,IAAI,cAAc;IAI3B,aAAa,IAAI,cAAc;IAIxC,KAAK,CAAC,EAAE,EAAE,MAAM,GAAG,SAAS,GAAG,SAAS;IAS/B,IAAI,IAAI,QAAQ;IAQhB,GAAG,IAAI,MAAM;IAIb,WAAW,IAAI,SAAS,GAAG,IAAI;IAO/B,WAAW,IAAI,SAAS,EAAE;IAuBpB,IAAI,CACjB,GAAG,EAAE,MAAM,EACX,OAAO,GAAE,WAAgB,GACxB,OAAO,CAAC,gBAAgB,GAAG,IAAI,CAAC;IAyCpB,UAAU,CACvB,IAAI,EAAE,MAAM,EACZ,OAAO,GAAE,cAAmB,GAC3B,OAAO,CAAC,IAAI,CAAC;IAaD,iBAAiB,CAC9B,OAAO,GAAE,cAAmB,GAC3B,OAAO,CAAC,gBAAgB,GAAG,IAAI,CAAC;IAiG1B,mBAAmB,IAAI,KAAK;IAIrC,IAAa,QAAQ,IAAI,OAAO,CAE/B;IAGK,cAAc,CAAC,IAAI,SAAS,OAAO,EAAE,EAAE,GAAG,EAC9C,IAAI,EAAE,MAAM,EACZ,KAAK,EAAE,CAAC,GAAG,IAAI,EAAE,IAAI,KAAK,SAAS,CAAC,GAAG,CAAC,GACvC,OAAO,CAAC,IAAI,CAAC;IAUV,qBAAqB,CAAC,IAAI,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IAYlD,gBAAgB,IAAI,OAAO,CAAC,UAAU,CAAC;IAqFvC,QAAQ,CAAC,OAAO,EAAE,iBAAiB,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC,IAAI,CAAC;IASpE,WAAW,CACf,OAAO,EAAE,iBAAiB,EAC1B,OAAO,EAAE,IAAI,CAAC,eAAe,CAAC,OAAO,GACpC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,eAAe,EAAE,CAAC;CAO1C"}