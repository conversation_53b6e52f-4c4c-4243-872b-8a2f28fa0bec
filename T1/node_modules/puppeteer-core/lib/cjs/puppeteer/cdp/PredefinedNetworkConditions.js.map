{"version": 3, "file": "PredefinedNetworkConditions.js", "sourceRoot": "", "sources": ["../../../../src/cdp/PredefinedNetworkConditions.ts"], "names": [], "mappings": ";AAAA;;;;GAIG;;;AAIH;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAiCG;AACU,QAAA,2BAA2B,GAAG,MAAM,CAAC,MAAM,CAAC;IACvD,kCAAkC;IAClC,sLAAsL;IACtL,SAAS,EAAE;QACT,gBAAgB;QAChB,QAAQ,EAAE,CAAC,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG;QAClC,cAAc;QACd,MAAM,EAAE,CAAC,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG;QAChC,YAAY;QACZ,OAAO,EAAE,GAAG,GAAG,CAAC;KACI;IACtB,SAAS,EAAE;QACT,iBAAiB;QACjB,QAAQ,EAAE,CAAC,CAAC,GAAG,GAAG,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG;QACzC,gBAAgB;QAChB,MAAM,EAAE,CAAC,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG;QAChC,YAAY;QACZ,OAAO,EAAE,GAAG,GAAG,IAAI;KACC;IACtB,kEAAkE;IAClE,sCAAsC;IACtC,SAAS,EAAE;QACT,iBAAiB;QACjB,QAAQ,EAAE,CAAC,CAAC,GAAG,GAAG,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG;QACzC,gBAAgB;QAChB,MAAM,EAAE,CAAC,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG;QAChC,YAAY;QACZ,OAAO,EAAE,GAAG,GAAG,IAAI;KACC;IACtB,SAAS,EAAE;QACT,cAAc;QACd,QAAQ,EAAE,CAAC,CAAC,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG;QACvC,cAAc;QACd,MAAM,EAAE,CAAC,CAAC,GAAG,GAAG,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG;QACvC,WAAW;QACX,OAAO,EAAE,EAAE,GAAG,IAAI;KACE;CACvB,CAAC,CAAC"}