{"version": 3, "file": "puppeteer.js", "sourceRoot": "", "sources": ["../../../src/puppeteer.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAIH,cAAc,2CAA2C,CAAC;AAE1D,OAAO,KAAK,aAAa,MAAM,2CAA2C,CAAC;AAE3E,OAAO,EAAC,gBAAgB,EAAC,MAAM,uBAAuB,CAAC;AAEvD,MAAM,aAAa,GAAG,gBAAgB,EAAE,CAAC;AAEzC;;GAEG;AACH,MAAM,SAAS,GAAG,IAAI,aAAa,CAAC,aAAa,CAAC;IAChD,eAAe,EAAE,KAAK;IACtB,aAAa;CACd,CAAC,CAAC;AAEH,MAAM,CAAC,MAAM;AACX;;GAEG;AACH,OAAO;AACP;;GAEG;AACH,WAAW;AACX;;GAEG;AACH,cAAc;AACd;;GAEG;AACH,MAAM;AACN;;GAEG;AACH,SAAS,GACV,GAAG,SAAS,CAAC;AAEd,eAAe,SAAS,CAAC"}