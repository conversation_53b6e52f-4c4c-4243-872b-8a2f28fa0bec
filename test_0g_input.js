#!/usr/bin/env node

// Simple test script untuk 0G Network input
const http = require('http');

function sendInput(input) {
    return new Promise((resolve, reject) => {
        const data = JSON.stringify({ input: input });
        
        const options = {
            hostname: 'localhost',
            port: 3000,
            path: '/api/programs/0g/input',
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Content-Length': data.length
            }
        };

        const req = http.request(options, (res) => {
            let body = '';
            res.on('data', (chunk) => {
                body += chunk;
            });
            res.on('end', () => {
                try {
                    const response = JSON.parse(body);
                    resolve({ status: res.statusCode, data: response });
                } catch (e) {
                    resolve({ status: res.statusCode, data: body });
                }
            });
        });

        req.on('error', (err) => {
            reject(err);
        });

        req.write(data);
        req.end();
    });
}

async function testInput() {
    console.log('🧪 Testing 0G Network Input...\n');

    const testInputs = ['5', '10', '1', '100'];

    for (const input of testInputs) {
        console.log(`Testing input: "${input}"`);
        
        try {
            const result = await sendInput(input);
            console.log(`   Status: ${result.status}`);
            console.log(`   Response: ${JSON.stringify(result.data)}`);
            
            if (result.status === 200) {
                console.log('   ✅ SUCCESS');
            } else {
                console.log('   ❌ FAILED');
            }
        } catch (error) {
            console.log(`   ❌ ERROR: ${error.message}`);
        }
        
        console.log('');
        await new Promise(resolve => setTimeout(resolve, 2000));
    }
}

if (require.main === module) {
    testInput().catch(console.error);
}
