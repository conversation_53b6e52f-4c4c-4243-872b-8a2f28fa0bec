# DEMO OUTPUT SETIAP PROGRAM TESTNET

## ANALISIS PROGRAM BERDASARKAN JENIS INTERAKSI

### 1. PROGRAM DENGAN BLESSED UI (Terminal Dashboard)
Program yang menggunakan blessed library untuk membuat interface terminal yang interaktif dengan navigasi arrow key.

#### A. MAITRIX AUTO BOT
- **Jenis**: Blessed UI dengan menu navigasi
- **Interaksi**: Arrow keys (↑↓) untuk navigasi, Enter untuk pilih, Ctrl+C untuk keluar
- **Output**:
```
                  ✦ ✦ MAITRIX AUTO BOT ✦ ✦                   

┌─ Transaction Logs ───────────────┐┌─ Informasi Wallet ───┐
│[ 18.49.55 ] Dont Forget To       ││ Wallet: 1/2 |        │
│Subscribe YT And Telegram         ││Address: 0xB4AD...2Ed1│
│@NTExhaust!!                      ││ ETH    :   0.0382 |  │
│[ 18.50.02 ] Wallet Information   ││azUSD  :   0.0000     │
│Updated !!                        ││ ATH    :  50.0000 |  │
│                                  │└──────────────────────┘
│                                  │┌─ Menu ───────────────┐
│                                  ││Auto Mint Token       │
│                                  ││Auto Claim Faucet     │
│                                  ││Auto Stake            │
│                                  ││Switch Wallet         │
```

#### B. T1 AUTO BRIDGE
- **Jenis**: Blessed UI dengan layout box
- **Interaksi**: Arrow keys untuk navigasi menu
- **Output**:
```
             __  ___/_  __ \_  / / /___  _/ \/ /             
             _____ \_  / / /  / / / __  / __  /              
             ____/ // /_/ // /_/ / __/ /  _  /               
             /____/ \____/ \____/  /___/  /_/                
                   ✦ ✦ T1 AUTO BRIDGE ✦ ✦                    

┌─ Transaction Logs ───────────────┐┌─ Informasi Wallet ───┐
│[ 18.51.11 ] JANGAN LUPA FOLLOW   ││┌── Address           │
│TIKTOK di @souiy1!!               ││: 0xB4AD...2Ed1       │
│[ 18.51.12 ] Saldo & Wallet       │││   ├── ETH Sepolia   │
│Updated !!                        ││: 0.0000              │
│                                  │││   └── ETH T1        │
│                                  │└──────────────────────┘
│                                  │┌─ Menu ───────────────┐
│                                  ││T1 Bridge             │
│                                  ││Clear Transaction Logs│
│                                  ││Refresh               │
│                                  ││Exit                  │
```

### 2. PROGRAM DENGAN CONSOLE MENU (Text-based)
Program yang menggunakan console biasa dengan menu berbasis angka.

#### A. RISE PROTOCOL
- **Jenis**: Console menu dengan input angka
- **Interaksi**: Ketik angka pilihan + Enter
- **Output**:
```
===============================================
              RISE TESTNET AUTO BOT
     x 
        Block: ******** | Gas: 0.00 Gwei 
===============================================

===== WALLET INFORMATION =====
Your address: ****************************************** 👤
ETH Balance: 0.000026080814958299 ETH 
WETH Balance: 0.0001712 WETH 
USDC Balance: 5.0 USDC 
Using proxy: None 🌐
=============================

===== MAIN MENU =====
1. Send to Random Addresses
2. Gas Pump
3. Inari Bank
4. Exit
More feature will add soon!
====================

Choose an option (1-4): 2

===== GAS PUMP MENU =====
1. Wrap ETH to WETH
2. Unwrap WETH to ETH
3. Approve WETH for DODO
4. Swap WETH to USDC
5. Swap USDC to WETH
6. Back to main menu
7. Auto Mode (Swap between modes 1-5)
========================
```

#### B. PHAROS PROTOCOL
- **Jenis**: Console menu dengan banyak pilihan
- **Interaksi**: Ketik angka pilihan + Enter
- **Output**:
```
██████╗     ██╗  ██╗     █████╗     ██████╗      ██████╗     ███████╗
██╔══██╗    ██║  ██║    ██╔══██╗    ██╔══██╗    ██╔═══██╗    ██╔════╝
██████╔╝    ███████║    ███████║    ██████╔╝    ██║   ██║    ███████╗
██╔═══╝     ██╔══██║    ██╔══██║    ██╔══██╗    ██║   ██║    ╚════██║
██║         ██║  ██║    ██║  ██║    ██║  ██║    ╚██████╔╝    ███████║
╚═╝         ╚═╝  ╚═╝    ╚═╝  ╚═╝    ╚═╝  ╚═╝     ╚═════╝     ╚══════╝

                     by fahril irham       
                  LETS FUCK THIS TESTNET                   

Enter number of transactions [5]: 3

=== Menu ===
1. Account Login
2. Account Check-in
3. Account Check
4. Claim Faucet PHRS
5. Claim Faucet USDC
6. Swap PHRS to USDC
7. Swap PHRS to USDT
8. Add Liquidity PHRS-USDC
9. Add Liquidity PHRS-USDT
10. Random Transfer
11. Social Task
12. Unlimited Faucet
13. Set Transaction Count
14. Exit
```

### 3. PROGRAM DENGAN PROMPT INPUT
Program yang meminta input spesifik dari user.

#### A. TRADEGPT AUTO BOT
- **Jenis**: Prompt-based input
- **Interaksi**: Ketik angka/text + Enter
- **Output**:
```
---------------------------------------------
  TradeGPT Auto Bot - Airdrop Insiders  
---------------------------------------------

[✓] Wallet Information for ******************************************:
[✓] Native (OG): 0.001180359466047717 OG
[✓] USDT: 4981.219417 USDT
[✓] Points: 200 (Mainnet: 100, Testnet: 100, Social: 0)
[✓] Last Updated: 2025-05-28T23:30:31.398Z

Enter the number of random chat prompts to send per wallet: 5

[➤] Processing wallet: ******************************************
[⟳] Sending chat request for wallet ******************************************: "Are there any upcoming airdrops?"
[✓] Chat request successful for wallet ******************************************: Are there any upcoming airdrops?
[⟳] Sending chat request for wallet ******************************************: "Swap 0.834542 USDT to LOP"
[✓] Chat request successful for wallet ******************************************: Swap 0.834542 USDT to LOP
```

### 4. PROGRAM AUTO-RUN (Tanpa Interaksi)
Program yang berjalan otomatis tanpa memerlukan input user.

#### A. ENSO FINANCE
- **Jenis**: Auto-run dengan banner
- **Interaksi**: Tidak ada, berjalan otomatis
- **Output**:
```
    ███╗   ██╗████████╗    ███████╗██╗  ██╗██╗  ██╗ █████╗ ██╗   ██╗███████╗████████╗
    ████╗  ██║╚══██╔══╝    ██╔════╝╚██╗██╔╝██║  ██║██╔══██╗██║   ██║██╔════╝╚══██╔══╝
    ██╔██╗ ██║   ██║       █████╗   ╚███╔╝ ███████║███████║██║   ██║███████╗   ██║   
    ██║╚██╗██║   ██║       ██╔══╝   ██╔██╗ ██╔══██║██╔══██║██║   ██║╚════██║   ██║   
    ██║ ╚████║   ██║       ███████╗██╔╝ ██╗██║  ██║██║  ██║╚██████╔╝███████║   ██║   
    ╚═╝  ╚═══╝   ╚═╝       ╚══════╝╚═╝  ╚═╝╚═╝  ╚═╝╚═╝  ╚═╝ ╚═════╝ ╚══════╝   ╚═╝   
                                                                                     
                ENSO AUTO BOT !!
```

### 5. PROGRAM DENGAN ERROR (Memerlukan Dependencies)

#### A. FAUCET TEST
- **Jenis**: Browser-based (Puppeteer)
- **Error**: Memerlukan browser dependencies
- **Output**:
```
Starting the faucet bot...
Error: Failed to launch the browser process!
[11179:11179:0601/184747.238594:FATAL:content/browser/zygote_host/zygote_host_impl_linux.cc:132] No usable sandbox! If you are running on Ubuntu 23.10+ or another Linux distro that has disabled unprivileged user namespaces with AppArmor, see https://chromium.googlesource.com/chromium/src/+/main/docs/security/apparmor-userns-restrictions.md. Otherwise see https://chromium.googlesource.com/chromium/src/+/main/docs/linux/suid_sandbox_development.md for more information on developing with the (older) SUID sandbox. If you want to live dangerously and need an immediate workaround, you can try using --no-sandbox.
```

## KESIMPULAN POLA INTERAKSI

1. **Blessed UI Programs**: Menggunakan arrow keys (↑↓←→), Enter, Escape, Ctrl+C
2. **Console Menu Programs**: Menggunakan input angka + Enter
3. **Prompt-based Programs**: Menggunakan input text/angka + Enter
4. **Auto-run Programs**: Tidak memerlukan interaksi, berjalan otomatis
5. **Browser-based Programs**: Memerlukan browser dependencies

## IMPLEMENTASI UNTUK WEB TERMINAL

Untuk mengimplementasikan di web terminal emulator:

1. **Deteksi jenis program** berdasarkan output pattern
2. **Handle blessed UI** dengan ANSI escape codes dan arrow key navigation
3. **Handle console menu** dengan input field untuk angka
4. **Handle prompt input** dengan input field untuk text
5. **Handle auto-run** dengan display output saja
6. **Handle errors** dengan pesan error yang jelas

Setiap program memiliki pattern output yang berbeda dan memerlukan handling interaksi yang berbeda pula.
