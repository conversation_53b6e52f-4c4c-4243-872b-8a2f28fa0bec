<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Testnet Program Terminal Emulator Demo</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #0d1117;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            color: #c9d1d9;
        }

        .demo-container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .demo-header {
            text-align: center;
            margin-bottom: 30px;
        }

        .demo-header h1 {
            color: #58a6ff;
            margin-bottom: 10px;
        }

        .demo-header p {
            color: #8b949e;
            font-size: 16px;
        }

        .demo-controls {
            background: #21262d;
            border: 1px solid #30363d;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .demo-controls h3 {
            margin-top: 0;
            color: #f0f6fc;
        }

        .program-buttons {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin-bottom: 20px;
        }

        .program-btn {
            background: #238636;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: background 0.2s;
        }

        .program-btn:hover {
            background: #2ea043;
        }

        .program-btn.blessed-ui {
            background: #8957e5;
        }

        .program-btn.blessed-ui:hover {
            background: #a475f9;
        }

        .program-btn.console-menu {
            background: #da3633;
        }

        .program-btn.console-menu:hover {
            background: #f85149;
        }

        .program-btn.prompt-input {
            background: #fb8500;
        }

        .program-btn.prompt-input:hover {
            background: #fd7e14;
        }

        .control-buttons {
            display: flex;
            gap: 10px;
            margin-top: 15px;
        }

        .control-btn {
            background: #6e7681;
            color: white;
            border: none;
            padding: 8px 12px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 12px;
        }

        .control-btn:hover {
            background: #8b949e;
        }

        .terminal-wrapper {
            background: #0d1117;
            border: 1px solid #30363d;
            border-radius: 8px;
            overflow: hidden;
        }

        #terminal-container {
            width: 100%;
            height: 500px;
        }

        .info-panel {
            background: #21262d;
            border: 1px solid #30363d;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
        }

        .info-panel h3 {
            margin-top: 0;
            color: #f0f6fc;
        }

        .program-types {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 15px;
        }

        .program-type {
            background: #161b22;
            border: 1px solid #30363d;
            border-radius: 6px;
            padding: 15px;
        }

        .program-type h4 {
            margin-top: 0;
            color: #58a6ff;
        }

        .program-type ul {
            margin: 10px 0;
            padding-left: 20px;
        }

        .program-type li {
            margin: 5px 0;
            color: #8b949e;
        }

        .legend {
            display: flex;
            gap: 15px;
            margin-bottom: 15px;
            flex-wrap: wrap;
        }

        .legend-item {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 12px;
        }

        .legend-color {
            width: 16px;
            height: 16px;
            border-radius: 3px;
        }

        .legend-color.blessed-ui { background: #8957e5; }
        .legend-color.console-menu { background: #da3633; }
        .legend-color.prompt-input { background: #fb8500; }
        .legend-color.auto-run { background: #238636; }
    </style>
</head>
<body>
    <div class="demo-container">
        <div class="demo-header">
            <h1>🚀 Testnet Program Terminal Emulator</h1>
            <p>Interactive terminal emulator that handles different types of testnet programs with proper input/output handling</p>
        </div>

        <div class="demo-controls">
            <h3>Demo Programs</h3>
            <div class="legend">
                <div class="legend-item">
                    <div class="legend-color blessed-ui"></div>
                    <span>Blessed UI (Arrow Keys)</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color console-menu"></div>
                    <span>Console Menu (Number Input)</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color prompt-input"></div>
                    <span>Prompt Input (Text Input)</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color auto-run"></div>
                    <span>Auto Run (No Input)</span>
                </div>
            </div>

            <div class="program-buttons">
                <button class="program-btn blessed-ui" onclick="simulateProgram('maitrix')">Maitrix (Blessed UI)</button>
                <button class="program-btn blessed-ui" onclick="simulateProgram('t1')">T1 Bridge (Blessed UI)</button>
                <button class="program-btn blessed-ui" onclick="simulateProgram('huddle')">Huddle (Blessed UI)</button>
                <button class="program-btn console-menu" onclick="simulateProgram('rise')">Rise Protocol (Menu)</button>
                <button class="program-btn console-menu" onclick="simulateProgram('pharos')">Pharos (Menu)</button>
                <button class="program-btn prompt-input" onclick="simulateProgram('tradegpt')">TradeGPT (Prompt)</button>
                <button class="program-btn prompt-input" onclick="simulateProgram('0g')">0G Network (Prompt)</button>
                <button class="program-btn prompt-input" onclick="simulateProgram('infts')">INFTS (Prompt)</button>
                <button class="program-btn prompt-input" onclick="simulateProgram('merak')">Merak SUI DEX (Prompt)</button>
                <button class="program-btn auto-run" onclick="simulateProgram('enso')">Enso Finance (Auto)</button>
                <button class="program-btn auto-run" onclick="simulateProgram('rome')">Rome EVM (Auto)</button>
                <button class="program-btn" onclick="simulateProgram('web-dashboard')">Union Web Dashboard</button>
                <button class="program-btn" onclick="simulateProgram('loading')">Kite/Shift (Loading)</button>
            </div>

            <div class="control-buttons">
                <button class="control-btn" onclick="terminal.clearOutput()">Clear Terminal</button>
                <button class="control-btn" onclick="terminal.stopProgram()">Stop Program</button>
                <button class="control-btn" onclick="showHelp()">Show Help</button>
            </div>
        </div>

        <div class="terminal-wrapper">
            <div id="terminal-container" class="terminal-container"></div>
        </div>

        <div class="info-panel">
            <h3>Program Types & Interaction Patterns</h3>
            <div class="program-types">
                <div class="program-type">
                    <h4>Blessed UI Programs</h4>
                    <ul>
                        <li>Use arrow keys (↑↓←→) for navigation</li>
                        <li>Enter to select menu items</li>
                        <li>Escape to go back</li>
                        <li>Ctrl+C to exit</li>
                        <li>Examples: Maitrix, T1, Huddle</li>
                    </ul>
                </div>
                <div class="program-type">
                    <h4>Console Menu Programs</h4>
                    <ul>
                        <li>Type number + Enter to select</li>
                        <li>Menu-driven interface</li>
                        <li>Clear option numbering</li>
                        <li>Examples: Rise, Pharos</li>
                    </ul>
                </div>
                <div class="program-type">
                    <h4>Prompt Input Programs</h4>
                    <ul>
                        <li>Type text/number + Enter</li>
                        <li>Specific prompts for input</li>
                        <li>May have validation</li>
                        <li>Examples: TradeGPT, 0G, INFTS</li>
                    </ul>
                </div>
                <div class="program-type">
                    <h4>Auto-Run Programs</h4>
                    <ul>
                        <li>No user interaction needed</li>
                        <li>Automatic execution</li>
                        <li>Progress display only</li>
                        <li>Examples: Enso, Rome</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script src="terminal-emulator-implementation.js"></script>
    <script>
        // Initialize terminal emulator
        const terminal = new TerminalEmulator('terminal-container');

        // Demo program outputs
        const demoOutputs = {
            maitrix: `                  ✦ ✦ MAITRIX AUTO BOT ✦ ✦

┌─ Transaction Logs ───────────────┐┌─ Informasi Wallet ───┐
│[ 18.49.55 ] Dont Forget To       ││ Wallet: 1/2 |        │
│Subscribe YT And Telegram         ││Address: 0xB4AD...2Ed1│
│@NTExhaust!!                      ││ ETH    :   0.0382 |  │
│[ 18.50.02 ] Wallet Information   ││azUSD  :   0.0000     │
│Updated !!                        ││ ATH    :  50.0000 |  │
│                                  │└──────────────────────┘
│                                  │┌─ Menu ───────────────┐
│                                  ││Auto Mint Token       │
│                                  ││Auto Claim Faucet     │
│                                  ││Auto Stake            │
│                                  ││Switch Wallet         │`,

            t1: `             __  ___/_  __ \_  / / /___  _/ \/ /
             _____ \_  / / /  / / / __  / __  /
             ____/ // /_/ // /_/ / __/ /  _  /
             /____/ \____/ \____/  /___/  /_/
                   ✦ ✦ T1 AUTO BRIDGE ✦ ✦

┌─ Transaction Logs ───────────────┐┌─ Informasi Wallet ───┐
│[ 18.51.11 ] JANGAN LUPA FOLLOW   ││┌── Address           │
│TIKTOK di @souiy1!!               ││: 0xB4AD...2Ed1       │
│[ 18.51.12 ] Saldo & Wallet       │││   ├── ETH Sepolia   │
│Updated !!                        ││: 0.0000              │
│                                  │││   └── ETH T1        │
│                                  │└──────────────────────┘
│                                  │┌─ Menu ───────────────┐
│                                  ││T1 Bridge             │
│                                  ││Clear Transaction Logs│
│                                  ││Refresh               │
│                                  ││Exit                  │`,

            huddle: `┌─ Deployment Logs ──────────────────────┐ ┌─ System Status ┐
│tokens to 0x7E7C65...                   │ │Wallet Status:  │
│2025-06-01T12:02:20.976Z | ? Sent 12.0  │ │Wallet 1: 0xB4AD│
│tokens to 0xDA24aA...                   │ │49...           │
│2025-06-01T12:02:24.702Z | ? Wallet     │ │                │
│(0xB4AD49...) balance: 0.00012626384    │ │Next Run:       │
│ETH                                     │ │Calculating...  │
│2025-06-01T12:02:24.705Z | ? Deploying  │ │                │
│token TRANSEXUALES (TRA)...             │ │                │
│2025-06-01T12:02:26.537Z | ? Awaiting   │ │                │
│deployment, tx hash: 0x492529...        │ │                │
│2025-06-01T12:02:27.054Z | ✅ Deployed   │ │                │
│at: 0x2185dE...                         │ │                │
│2025-06-01T12:02:27.061Z | [2] ✅        │ │                │
│Deployed TRANSEXUALES (TRA) at          │ │                │
│0x2185dE...                             │ │                │
└────────────────────────────────────────┘ └────────────────┘
Press Q or ESC to exit`,

            rise: `===============================================
              RISE TESTNET AUTO BOT
     x
        Block: ******** | Gas: 0.00 Gwei
===============================================

===== WALLET INFORMATION =====
Your address: ****************************************** 👤
ETH Balance: 0.000026080814958299 ETH
WETH Balance: 0.0001712 WETH
USDC Balance: 5.0 USDC
Using proxy: None 🌐
=============================

===== MAIN MENU =====
1. Send to Random Addresses
2. Gas Pump
3. Inari Bank
4. Exit
More feature will add soon!
====================

Choose an option (1-4): `,

            pharos: `██████╗     ██╗  ██╗     █████╗     ██████╗      ██████╗     ███████╗
██╔══██╗    ██║  ██║    ██╔══██╗    ██╔══██╗    ██╔═══██╗    ██╔════╝
██████╔╝    ███████║    ███████║    ██████╔╝    ██║   ██║    ███████╗
██╔═══╝     ██╔══██║    ██╔══██║    ██╔══██╗    ██║   ██║    ╚════██║
██║         ██║  ██║    ██║  ██║    ██║  ██║    ╚██████╔╝    ███████║
╚═╝         ╚═╝  ╚═╝    ╚═╝  ╚═╝    ╚═╝  ╚═╝     ╚═════╝     ╚══════╝

                     by fahril irham
                  LETS FUCK THIS TESTNET

=== Menu ===
1. Account Login
2. Account Check-in
3. Account Check
4. Claim Faucet PHRS
5. Claim Faucet USDC
6. Swap PHRS to USDC
7. Swap PHRS to USDT
8. Add Liquidity PHRS-USDC
9. Add Liquidity PHRS-USDT
10. Random Transfer
11. Social Task
12. Unlimited Faucet
13. Set Transaction Count
14. Exit

Choose an option (1-14): `,

            tradegpt: `---------------------------------------------
  TradeGPT Auto Bot - Airdrop Insiders
---------------------------------------------

[✓] Wallet Information for ******************************************:
[✓] Native (OG): 0.001180359466047717 OG
[✓] USDT: 4981.219417 USDT
[✓] Points: 200 (Mainnet: 100, Testnet: 100, Social: 0)
[✓] Last Updated: 2025-05-28T23:30:31.398Z

Enter the number of random chat prompts to send per wallet: `,

            '0g': `░▄▀▄░█▀▀░░░░░█▀▄░█▀█░▀█▀
░█/█░█░█░▄▄▄░█▀▄░█░█░░█░
░░▀░░▀▀▀░░░░░▀▀░░▀▀▀░░▀░
  By : El Puqus Airdrop
   github.com/ahlulmukh
 Use it at your own risk

Total transaction perday? `,

            infts: `---------------------------------------------
  INFTs Auto Bot - Airdrop Insiders
---------------------------------------------

Enter the number of chat interactions to perform: `,

            merak: `  ____  _   _ ___     ____  _______  __    ____   ___ _____
 / ___|| | | |_ _|   |  _ \| ____\ \/ /   | __ ) / _ \_   _|
 \___ \| | | || |    | | | |  _|  \  /    |  _ \| | | || |
  ___) | |_| || |    | |_| | |___ /  \    | |_) | |_| || |
 |____/ \___/|___|   |____/|_____/_/\_\   |____/ \___/ |_|

🚀 Automating Wrap, Swap, and Liquidity Created By Kazuha787

? How many times to wrap SUI -> wSUI? (0)`,

            enso: `    ███╗   ██╗████████╗    ███████╗██╗  ██╗██╗  ██╗ █████╗ ██╗   ██╗███████╗████████╗
    ████╗  ██║╚══██╔══╝    ██╔════╝╚██╗██╔╝██║  ██║██╔══██╗██║   ██║██╔════╝╚══██╔══╝
    ██╔██╗ ██║   ██║       █████╗   ╚███╔╝ ███████║███████║██║   ██║███████╗   ██║
    ██║╚██╗██║   ██║       ██╔══╝   ██╔██╗ ██╔══██║██╔══██║██║   ██║╚════██║   ██║
    ██║ ╚████║   ██║       ███████╗██╔╝ ██╗██║  ██║██║  ██║╚██████╔╝███████║   ██║
    ╚═╝  ╚═══╝   ╚═╝       ╚══════╝╚═╝  ╚═╝╚═╝  ╚═╝╚═╝  ╚═╝ ╚═════╝ ╚══════╝   ╚═╝

                ENSO AUTO BOT !!

[✓] Starting automated process...
[✓] Loading accounts...
[✓] Processing transactions...`,

            rome: `📦 Attempt #1 - 19.04.42
🔧 Kontrak: MyContract
🌐 Network: rome.testnet.romeprotocol.xyz
👛 Wallet: ******************************************
⚙️ Constructor args: 100

[✓] Starting deployment process...
[⟳] Connecting to network...
[✅] Network connection established
[⟳] Deploying contract...
[✅] Contract deployed successfully!`,

            'web-dashboard': `🚀 Testnet Program Dashboard Started!
📊 Server running on: http://localhost:3000
📋 Managing 23 programs:
   1. 0G Network (blockchain)
   2. 0G Storage (storage)
   3. Aster AutoBot NTE (bot)
   ...
   23. Union Auto Bot (protocol)

✨ Features:
   • Auto dependency installation
   • Real-time console output
   • Interactive program input
   • File editing capabilities
   • Build process automation

🌐 Open your browser and navigate to the URL above to get started!`,



            loading: `    ███╗   ██╗████████╗    ███████╗██╗  ██╗██╗  ██╗ █████╗ ██╗   ██╗███████╗████████╗
    ████╗  ██║╚══██╔══╝    ██╔════╝╚██╗██╔╝██║  ██║██╔══██╗██║   ██║██╔════╝╚══██╔══╝
    ██╔██╗ ██║   ██║       █████╗   ╚███╔╝ ███████║███████║██║   ██║███████╗   ██║
    ██║╚██╗██║   ██║       ██╔══╝   ██╔██╗ ██╔══██║██╔══██║██║   ██║╚════██║   ██║
    ██║ ╚████║   ██║       ███████╗██╔╝ ██╗██║  ██║██║  ██║╚██████╔╝███████║   ██║
    ╚═╝  ╚═══╝   ╚═╝       ╚══════╝╚═╝  ╚═╝╚═╝  ╚═╝╚═╝  ╚═╝ ╚═════╝ ╚══════╝   ╚═╝

     === Telegram Channel 🚀 : NT Exhaust ( @NTExhaust ) ===
               ✪ KITE AI AUTO DAILY QUIZ & CHAT AI ✪

Loading...`
        };

        function simulateProgram(programName) {
            terminal.startProgram(programName);

            // Simulate program output
            setTimeout(() => {
                terminal.processOutput(demoOutputs[programName] || 'Program output not available');
            }, 500);
        }

        function showHelp() {
            const helpText = `
Terminal Emulator Help:

Blessed UI Programs:
- Use arrow keys (↑↓←→) to navigate
- Press Enter to select
- Press Escape to go back
- Press Ctrl+C to exit

Console Menu Programs:
- Type the number of your choice
- Press Enter to confirm

Prompt Input Programs:
- Type your input (text or numbers)
- Press Enter to submit

Auto-Run Programs:
- No interaction needed
- Watch the output

Web Dashboard Programs:
- Click the "Open Dashboard" button when it appears

General Controls:
- Use the control buttons above to manage the terminal
- Clear Terminal: Clears all output
- Stop Program: Stops the current program
- Show Help: Shows this help message
            `;

            terminal.processOutput(helpText);
        }

        // Listen for terminal input events
        document.getElementById('terminal-container').addEventListener('terminalInput', (e) => {
            console.log('Terminal input:', e.detail);

            // Simulate program responses based on input
            setTimeout(() => {
                simulateResponse(e.detail.input, e.detail.programType);
            }, 1000);
        });

        function simulateResponse(input, programType) {
            let response = '';

            switch (programType) {
                case 'console-menu':
                    response = `\nProcessing option ${input}...\n[✓] Option ${input} executed successfully!\n\nReturning to main menu...`;
                    break;
                case 'prompt-input':
                    if (input.match(/^\d+$/)) {
                        response = `\n[✓] Input received: ${input}\n[⟳] Processing ${input} operations...\n[✅] All operations completed successfully!\n[ℹ] Total processed: ${input}`;
                    } else {
                        response = `\n[✓] Input received: ${input}\n[⟳] Processing...\n[✅] Operation completed successfully!`;
                    }
                    break;
                case 'blessed-ui':
                    response = `\n[UI] Menu navigation detected\n[✓] Interface updated`;
                    break;
                default:
                    response = `\nCommand executed: ${input}`;
            }

            terminal.processOutput(response);
        }

        // Initialize with welcome message
        setTimeout(() => {
            terminal.processOutput('Welcome to Testnet Program Terminal Emulator!\nClick any program button above to start a demo.\n\nType "help" for more information.\n');
        }, 1000);
    </script>
</body>
</html>
