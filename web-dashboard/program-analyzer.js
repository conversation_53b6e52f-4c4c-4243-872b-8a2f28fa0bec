// Program Analyzer - Analyze how each program works and their interaction patterns

const programAnalysis = {
  // Blessed UI Programs (Interactive Terminal UI)
  blessedPrograms: {
    'maitrix': {
      type: 'blessed-ui',
      interface: 'menu-based',
      navigation: 'arrow-keys',
      menuItems: [
        'Auto Mint Token',
        'Auto Claim Faucet',
        'Auto Stake',
        'Switch Wallet',
        'Antrian Transaksi',
        'Clear Transaction Logs',
        'Refresh',
        'Exit'
      ],
      keyBindings: {
        'up/down': 'navigate menu',
        'enter': 'select menu item',
        'escape': 'exit/back',
        'ctrl+c': 'exit program'
      },
      uiElements: [
        'Transaction Logs Box',
        'Wallet Info Box',
        'Main Menu List',
        'Sub Menus'
      ]
    },

    't1': {
      type: 'blessed-ui',
      interface: 'menu-based',
      navigation: 'arrow-keys',
      menuItems: [
        'T1 Bridge',
        'Clear Transaction Logs',
        'Refresh',
        'Exit',
        'Stop All Transactions'
      ],
      subMenus: {
        'T1 Bridge': [
          'Bridge ETH to T1',
          'Bridge T1 to ETH',
          'Check Balance',
          'Back to Main Menu'
        ]
      },
      keyBindings: {
        'up/down': 'navigate menu',
        'enter': 'select menu item',
        'escape': 'exit/back'
      }
    },

    'union': {
      type: 'blessed-ui',
      interface: 'dashboard',
      navigation: 'arrow-keys',
      uiElements: [
        'Status Bar',
        'Wallet Info Table',
        'Transaction Logs',
        'Control Menu'
      ],
      keyBindings: {
        'q/escape': 'exit',
        'up/down': 'navigate',
        'enter': 'select'
      }
    },

    'inco': {
      type: 'blessed-ui',
      interface: 'menu-based',
      navigation: 'arrow-keys',
      menuItems: [
        'Inco Tool',
        'Antrian Transaksi',
        'Stop All Transactions',
        'Clear Transaction Logs',
        'Refresh',
        'Exit'
      ]
    },

    'r2nte': {
      type: 'blessed-ui',
      interface: 'dashboard',
      navigation: 'arrow-keys',
      title: 'NT EXHAUST',
      uiElements: [
        'Header with ASCII Art',
        'Description Box',
        'Transaction Logs',
        'Wallet Info',
        'Main Menu'
      ]
    }
  },

  // Console-based Programs (Text Menu)
  consolePrograms: {
    'rise': {
      type: 'console-menu',
      interface: 'text-based',
      navigation: 'number-input',
      menuStructure: {
        'main': [
          '1. Send to Random Addresses',
          '2. Gas Pump',
          '3. Inari Bank',
          '4. Exit'
        ],
        'gasPump': [
          '1. Wrap ETH to WETH',
          '2. Unwrap WETH to ETH',
          '3. Approve WETH for DODO',
          '4. Swap WETH to USDC',
          '5. Swap USDC to WETH',
          '6. Back to main menu',
          '7. Auto Mode'
        ]
      },
      interaction: 'readline-input',
      prompt: 'Choose an option (1-4): '
    },

    'tradegpt': {
      type: 'console-prompt',
      interface: 'prompt-based',
      interaction: 'readline-input',
      flow: [
        'Display wallet info',
        'Prompt for number of chat prompts',
        'Execute automated trading',
        'Display final results'
      ],
      prompts: [
        'Enter the number of random chat prompts to send per wallet: '
      ]
    }
  },

  // Auto-run Programs (No interaction needed)
  autoPrograms: {
    'merak': {
      type: 'auto-run',
      interface: 'automated',
      interaction: 'none',
      description: 'SUI DEX automation - runs automatically'
    },

    '0g': {
      type: 'auto-run',
      interface: 'automated',
      interaction: 'none',
      description: '0G Network automation - runs automatically'
    }
  }
};

// Program interaction patterns
const interactionPatterns = {
  // How to handle blessed UI programs
  blessedUI: {
    initialization: [
      'Detect blessed UI start sequences',
      'Clear screen and show UI indicator',
      'Enable arrow key navigation',
      'Show blessed UI controls'
    ],
    navigation: [
      'Arrow Up/Down: Navigate menu items',
      'Enter: Select menu item',
      'Escape: Go back/exit',
      'Ctrl+C: Force exit'
    ],
    rendering: [
      'Process ANSI escape codes',
      'Handle box drawing characters',
      'Maintain terminal-like appearance',
      'Show selection highlights'
    ]
  },

  // How to handle console menu programs
  consoleMenu: {
    initialization: [
      'Wait for menu display',
      'Show number input prompt',
      'Enable text input field'
    ],
    interaction: [
      'Type number choice',
      'Press Enter to submit',
      'Wait for next menu/action'
    ]
  },

  // How to handle prompt-based programs
  promptBased: {
    initialization: [
      'Wait for prompt text',
      'Enable text input field',
      'Show prompt clearly'
    ],
    interaction: [
      'Type response to prompt',
      'Press Enter to submit',
      'Wait for next prompt or completion'
    ]
  }
};

// Export for use in main application
export { programAnalysis, interactionPatterns };
