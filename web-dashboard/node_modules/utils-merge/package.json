{"name": "utils-merge", "version": "1.0.1", "description": "merge() utility function", "keywords": ["util"], "author": {"name": "<PERSON>", "email": "jared<PERSON><PERSON>@gmail.com", "url": "http://www.jaredhanson.net/"}, "repository": {"type": "git", "url": "git://github.com/jaredhanson/utils-merge.git"}, "bugs": {"url": "http://github.com/jaredhanson/utils-merge/issues"}, "license": "MIT", "licenses": [{"type": "MIT", "url": "http://opensource.org/licenses/MIT"}], "main": "./index", "dependencies": {}, "devDependencies": {"make-node": "0.3.x", "mocha": "1.x.x", "chai": "1.x.x"}, "engines": {"node": ">= 0.4.0"}, "scripts": {"test": "node_modules/.bin/mocha --reporter spec --require test/bootstrap/node test/*.test.js"}}