# Setup Guide - Testnet Program Dashboard

Panduan lengkap untuk mengatur dan menja<PERSON>an semua program testnet melalui dashboard web.

## 🚀 Quick Start

1. **Clone atau pastikan semua program testnet ada di direktori yang benar**
2. **Install dashboard dependencies**:
   ```bash
   cd web-dashboard
   npm install
   ```
3. **Jalankan dashboard**:
   ```bash
   npm start
   ```
4. **Buka browser**: `http://localhost:3000`

## 📋 Program Setup Requirements

### Programs yang Memerlukan Environment Variables

Beberapa program memerlukan file konfigurasi atau environment variables:

#### 1. **TradeGPT Auto Bot**
- **File diperlukan**: `.env`
- **Isi file**:
  ```env
  PRIVATE_KEY_1=your_private_key_here
  PRIVATE_KEY_2=your_private_key_here
  # Add more private keys as needed
  ```

#### 2. **Rise Protocol**
- **File diperlukan**: `.env`
- **Isi file**:
  ```env
  PRIVATE_KEYS=private_key1,private_key2,private_key3
  RPC_URL=https://testnet-rpc.risenetwork.io
  ```

#### 3. **Union Auto Bot**
- **File diperlukan**: `wallet.json`
- **Format file**:
  ```json
  {
    "wallets": [
      {
        "privateKey": "your_private_key_here",
        "address": "wallet_address_here"
      }
    ]
  }
  ```

#### 4. **Kite AI Auto Bot**
- **File diperlukan**: `accounts.txt`
- **Format file**: Satu private key per baris
  ```
  private_key_1
  private_key_2
  private_key_3
  ```

#### 5. **Merak SUI DEX Bot**
- **File diperlukan**: `pvkey.txt`
- **Format file**: Satu private key per baris
  ```
  sui_private_key_1
  sui_private_key_2
  ```

#### 6. **T1 Auto Bridge**
- **File diperlukan**: `.env`
- **Isi file**:
  ```env
  PRIVATE_KEYS=key1,key2,key3
  ```

#### 7. **Inco Network**
- **File diperlukan**: `.env`
- **Isi file**:
  ```env
  PRIVATE_KEYS=private_key1,private_key2
  ```

### Programs yang Memerlukan Build Process

#### 1. **0G Network**
- Memerlukan TypeScript compilation
- Dashboard akan otomatis menjalankan `npm run build`
- Pastikan TypeScript dependencies terinstall

## 🔧 Setup Individual Programs

### Cara Setup Cepat untuk Semua Program:

1. **Buka dashboard di browser**
2. **Klik program yang ingin disetup**
3. **Pergi ke tab "Files"** untuk melihat file yang diperlukan
4. **Pergi ke tab "Config"** untuk mengedit file konfigurasi
5. **Buat atau edit file yang diperlukan** (seperti .env, accounts.txt, dll)
6. **Klik "Save"** untuk menyimpan
7. **Kembali ke tab "Console"** dan klik "Start"

### Template File Konfigurasi

#### Template .env untuk program blockchain:
```env
# Private Keys (pisahkan dengan koma jika multiple)
PRIVATE_KEYS=your_private_key_1,your_private_key_2

# RPC URLs
RPC_URL=https://rpc-url-here
TESTNET_RPC=https://testnet-rpc-here

# API Keys (jika diperlukan)
API_KEY=your_api_key_here

# Other configurations
DELAY_MIN=1000
DELAY_MAX=5000
```

#### Template accounts.txt:
```
******************************************
******************************************
******************************************
```

#### Template wallet.json:
```json
{
  "wallets": [
    {
      "privateKey": "******************************************90abcdef1234567890abcdef12",
      "address": "******************************************"
    }
  ],
  "config": {
    "delayMin": 1000,
    "delayMax": 5000,
    "gasLimit": 21000
  }
}
```

## 🔍 Troubleshooting

### Program Tidak Bisa Start

1. **Cek Dependencies**:
   - Dashboard akan otomatis install dependencies
   - Jika gagal, cek console untuk error message

2. **Cek File Konfigurasi**:
   - Pastikan file .env, accounts.txt, atau wallet.json ada
   - Pastikan format file benar
   - Pastikan private keys valid

3. **Cek Build Process**:
   - Untuk program TypeScript, pastikan build berhasil
   - Lihat output build di console

### Program Berhenti Tiba-tiba

1. **Cek Console Output**:
   - Lihat error message di console
   - Biasanya karena private key invalid atau RPC error

2. **Cek Network Connection**:
   - Pastikan koneksi internet stabil
   - Cek apakah RPC endpoint masih aktif

### Input Tidak Berfungsi

1. **Pastikan Program Running**:
   - Input field hanya aktif saat program berjalan
   - Restart program jika perlu

2. **Cek Program Type**:
   - Tidak semua program memerlukan input interaktif
   - Beberapa program berjalan otomatis

## 📝 Tips & Best Practices

### Keamanan Private Keys
- **Jangan share private keys** di file yang bisa diakses publik
- **Backup private keys** di tempat yang aman
- **Gunakan testnet keys** untuk testing

### Performance
- **Jangan jalankan terlalu banyak program bersamaan** untuk menghindari rate limiting
- **Monitor resource usage** komputer Anda
- **Restart program** jika mengalami memory leak

### Monitoring
- **Selalu monitor console output** untuk melihat status program
- **Cek log errors** untuk troubleshooting
- **Gunakan filter** untuk mencari program tertentu

## 🆘 Support

Jika mengalami masalah:

1. **Cek console browser** (F12) untuk JavaScript errors
2. **Cek terminal server** untuk backend errors
3. **Restart dashboard** jika diperlukan:
   ```bash
   # Stop server (Ctrl+C)
   # Then restart
   npm start
   ```

## 🔄 Update Programs

Untuk update program individual:
1. **Stop program** yang sedang berjalan
2. **Update source code** program
3. **Restart dashboard** jika diperlukan
4. **Start program** kembali

Dashboard akan otomatis handle dependencies dan build process yang diperlukan.

---

**Happy Testing! 🎉**
