import express from 'express';
import { createServer } from 'http';
import { Server } from 'socket.io';
import cors from 'cors';
import fs from 'fs-extra';
import { spawn } from 'child_process';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const app = express();
const server = createServer(app);
const io = new Server(server, {
  cors: {
    origin: "*",
    methods: ["GET", "POST"]
  }
});

app.use(cors());
app.use(express.json());
app.use(express.static('public'));

// Daftar semua program yang tersedia dengan konfigurasi yang benar
const programs = [
  {
    id: '0g',
    name: '0G Network',
    description: 'Zero Gravity blockchain network program - Auto swap 0g-testnet',
    directory: '0g',
    command: 'npm',
    args: ['start'],
    type: 'blockchain',
    hasPackageJson: true,
    buildRequired: true,
    buildCommand: 'npm run build'
  },
  {
    id: '0g-storage',
    name: '0G Storage',
    description: 'Zero Gravity storage network',
    directory: '0g-Storage',
    command: 'npm',
    args: ['start'],
    type: 'storage',
    hasPackageJson: true
  },
  {
    id: 'aster-autobot',
    name: 'Aster AutoBot NTE',
    description: 'Automated bot for Aster network',
    directory: 'AsterAutoBot-NTE',
    command: 'npm',
    args: ['start'],
    type: 'bot',
    hasPackageJson: true
  },
  {
    id: 'byte',
    name: 'Byte Protocol',
    description: 'Byte protocol automation',
    directory: 'Byte',
    command: 'npm',
    args: ['start'],
    type: 'protocol',
    hasPackageJson: true
  },
  {
    id: 'coinsif',
    name: 'Coinsif Referral',
    description: 'Coinsif referral system',
    directory: 'coinsif',
    command: 'node',
    args: ['reff.js'],
    type: 'referral',
    hasPackageJson: true
  },
  {
    id: 'enso',
    name: 'Enso Finance',
    description: 'Enso finance automation',
    directory: 'Enso',
    command: 'npm',
    args: ['start'],
    type: 'defi',
    hasPackageJson: true
  },
  {
    id: 'euclid-bot',
    name: 'Euclid Bot',
    description: 'Euclid protocol automation bot',
    directory: 'Euclid-Bot',
    command: 'npm',
    args: ['start'],
    type: 'bot',
    hasPackageJson: true
  },
  {
    id: 'faucet-test',
    name: 'Faucet Test',
    description: 'Automated faucet testing',
    directory: 'faucet-test',
    command: 'npm',
    args: ['start'],
    type: 'testing',
    hasPackageJson: false
  },
  {
    id: 'huddle',
    name: 'Huddle Protocol',
    description: 'Huddle deployment automation',
    directory: 'Huddle',
    command: 'node',
    args: ['deploy.js'],
    type: 'deployment',
    hasPackageJson: true
  },
  {
    id: 'inco',
    name: 'Inco Network',
    description: 'INCO Auto Bot - NT Exhaust automation',
    directory: 'Inco',
    command: 'npm',
    args: ['start'],
    type: 'blockchain',
    hasPackageJson: true
  },
  {
    id: 'infts',
    name: 'INFTS Protocol',
    description: 'INFTS protocol automation',
    directory: 'INFTS',
    command: 'npm',
    args: ['start'],
    type: 'nft',
    hasPackageJson: true
  },
  {
    id: 'kite',
    name: 'Kite AI Auto Bot',
    description: 'NT EXHAUST - KITE AI AUTO BOT',
    directory: 'kite',
    command: 'npm',
    args: ['start'],
    type: 'protocol',
    hasPackageJson: true
  },
  {
    id: 'maitrix',
    name: 'Maitrix Network',
    description: 'Maitrix network automation',
    directory: 'Maitrix',
    command: 'npm',
    args: ['start'],
    type: 'blockchain',
    hasPackageJson: true
  },
  {
    id: 'merak',
    name: 'Merak SUI DEX Bot',
    description: 'SUI DEX automation bot for wrapping, swapping, and liquidity',
    directory: 'merak',
    command: 'npm',
    args: ['start'],
    type: 'defi',
    hasPackageJson: true,
    isInteractive: true,
    requiresFiles: ['pvkey.txt']
  },
  {
    id: 'monad-testnet',
    name: 'Monad Testnet',
    description: 'Monad testnet automation',
    directory: 'MonadTestnet',
    command: 'npm',
    args: ['start'],
    type: 'testnet',
    hasPackageJson: true
  },
  {
    id: 'pharos',
    name: 'Pharos Protocol',
    description: 'Pharos protocol automation',
    directory: 'Pharos',
    command: 'node',
    args: ['main.js'],
    type: 'protocol',
    hasPackageJson: true
  },
  {
    id: 'pharos-asli',
    name: 'Pharos Original',
    description: 'Original Pharos implementation',
    directory: 'Pharos-asli',
    command: 'node',
    args: ['main.js'],
    type: 'protocol',
    hasPackageJson: true
  },
  {
    id: 'r2',
    name: 'R2 Protocol',
    description: 'R2 protocol automation',
    directory: 'R2',
    command: 'npm',
    args: ['start'],
    type: 'protocol',
    hasPackageJson: true
  },
  {
    id: 'r2nte',
    name: 'R2 NTE',
    description: 'R2 NTE automation',
    directory: 'R2nte',
    command: 'npm',
    args: ['start'],
    type: 'protocol',
    hasPackageJson: true
  },
  {
    id: 'rise',
    name: 'Rise Protocol',
    description: 'Rise Testnet Bot - Random transfers, Gas Pump operations, and Inari Bank functions',
    directory: 'Rise',
    command: 'npm',
    args: ['start'],
    type: 'protocol',
    hasPackageJson: true,
    isInteractive: true,
    requiresEnv: true
  },
  {
    id: 'rome',
    name: 'Rome EVM Deployer',
    description: 'EVM contract deployment automation',
    directory: 'rome',
    command: 'npm',
    args: ['start'],
    type: 'deployment',
    hasPackageJson: true
  },
  {
    id: 'shift',
    name: 'Shift Protocol',
    description: 'Shift protocol automation',
    directory: 'Shift',
    command: 'npm',
    args: ['start'],
    type: 'protocol',
    hasPackageJson: true
  },
  {
    id: 't1',
    name: 'T1 Auto Bridge',
    description: 'SOUIY - T1 Auto Bridge protocol automation',
    directory: 'T1',
    command: 'npm',
    args: ['start'],
    type: 'protocol',
    hasPackageJson: true
  },
  {
    id: 'tradegpt',
    name: 'TradeGPT Auto Bot',
    description: 'Automated bot for TradeGPT Finance on 0G Testnet',
    directory: 'TradeGPT',
    command: 'npm',
    args: ['start'],
    type: 'trading',
    hasPackageJson: true
  },
  {
    id: 'union',
    name: 'Union Auto Bot',
    description: 'Union Testnet automation bot with Blessed terminal UI',
    directory: 'Union',
    command: 'node',
    args: ['main.js'],
    type: 'protocol',
    hasPackageJson: true,
    isInteractive: true,
    requiresFiles: ['wallet.json']
  }
];

// Store running processes
const runningProcesses = new Map();

// Helper function to check if dependencies are installed
async function checkDependencies(programPath) {
  try {
    const nodeModulesPath = path.join(programPath, 'node_modules');
    const packageJsonPath = path.join(programPath, 'package.json');

    if (!await fs.pathExists(packageJsonPath)) {
      return { installed: false, hasPackageJson: false };
    }

    const hasNodeModules = await fs.pathExists(nodeModulesPath);
    return { installed: hasNodeModules, hasPackageJson: true };
  } catch (error) {
    return { installed: false, hasPackageJson: false };
  }
}

// Helper function to check required files
async function checkRequiredFiles(programPath, requiredFiles) {
  if (!requiredFiles || requiredFiles.length === 0) {
    return { allPresent: true, missingFiles: [] };
  }

  const missingFiles = [];

  for (const file of requiredFiles) {
    const filePath = path.join(programPath, file);
    const exists = await fs.pathExists(filePath);
    if (!exists) {
      missingFiles.push(file);
    }
  }

  return {
    allPresent: missingFiles.length === 0,
    missingFiles
  };
}

// Helper function to create template files
async function createTemplateFile(programPath, fileName, programId) {
  const filePath = path.join(programPath, fileName);
  let content = '';

  switch (fileName) {
    case 'pvkey.txt':
      content = '# Add your SUI private keys here, one per line\n# Example:\n# suiprivkey1abc123...\n# suiprivkey2def456...\n';
      break;
    case 'accounts.txt':
      content = '# Add your private keys here, one per line\n# Example:\n# 0x1234567890abcdef...\n# 0xabcdef1234567890...\n';
      break;
    case '.env':
      content = '# Environment variables for this program\n# Add your private keys and configuration here\n\n# Example:\n# PRIVATE_KEYS=key1,key2,key3\n# RPC_URL=https://rpc-url-here\n# API_KEY=your_api_key_here\n';
      break;
    case 'wallet.json':
      content = JSON.stringify({
        wallets: [
          {
            privateKey: "your_private_key_here",
            address: "your_wallet_address_here"
          }
        ],
        config: {
          delayMin: 1000,
          delayMax: 5000
        }
      }, null, 2);
      break;
    default:
      content = `# Configuration file for ${fileName}\n# Please add your configuration here\n`;
  }

  await fs.writeFile(filePath, content, 'utf8');
  return content;
}

// Helper function to install dependencies
async function installDependencies(programPath, programId) {
  return new Promise((resolve, reject) => {
    const installProcess = spawn('npm', ['install'], {
      cwd: programPath,
      stdio: ['pipe', 'pipe', 'pipe']
    });

    let output = '';
    let errorOutput = '';

    installProcess.stdout.on('data', (data) => {
      const message = data.toString();
      output += message;
      io.emit('program-output', {
        programId: programId,
        type: 'install',
        data: `[INSTALL] ${message}`
      });
    });

    installProcess.stderr.on('data', (data) => {
      const message = data.toString();
      errorOutput += message;
      io.emit('program-output', {
        programId: programId,
        type: 'install-error',
        data: `[INSTALL ERROR] ${message}`
      });
    });

    installProcess.on('close', (code) => {
      if (code === 0) {
        resolve({ success: true, output });
      } else {
        reject({ success: false, error: errorOutput, code });
      }
    });

    installProcess.on('error', (error) => {
      reject({ success: false, error: error.message });
    });
  });
}

// Helper function to build project if needed
async function buildProject(programPath, programId, buildCommand) {
  return new Promise((resolve, reject) => {
    const [command, ...args] = buildCommand.split(' ');
    const buildProcess = spawn(command, args, {
      cwd: programPath,
      stdio: ['pipe', 'pipe', 'pipe']
    });

    let output = '';
    let errorOutput = '';

    buildProcess.stdout.on('data', (data) => {
      const message = data.toString();
      output += message;
      io.emit('program-output', {
        programId: programId,
        type: 'build',
        data: `[BUILD] ${message}`
      });
    });

    buildProcess.stderr.on('data', (data) => {
      const message = data.toString();
      errorOutput += message;
      io.emit('program-output', {
        programId: programId,
        type: 'build-error',
        data: `[BUILD ERROR] ${message}`
      });
    });

    buildProcess.on('close', (code) => {
      if (code === 0) {
        resolve({ success: true, output });
      } else {
        reject({ success: false, error: errorOutput, code });
      }
    });

    buildProcess.on('error', (error) => {
      reject({ success: false, error: error.message });
    });
  });
}

// API endpoints
app.get('/api/programs', (req, res) => {
  res.json(programs);
});

app.get('/api/running-programs', (req, res) => {
  const running = [];
  runningProcesses.forEach((processInfo, id) => {
    const runtime = new Date() - processInfo.startTime;
    running.push({
      id: id,
      name: processInfo.programName,
      startTime: processInfo.startTime,
      runtime: Math.floor(runtime / 1000)
    });
  });
  res.json(running);
});

app.get('/api/programs/:id/status', (req, res) => {
  const { id } = req.params;
  const processInfo = runningProcesses.get(id);
  const isRunning = !!processInfo;

  if (isRunning) {
    const runtime = new Date() - processInfo.startTime;
    res.json({
      running: true,
      startTime: processInfo.startTime,
      runtime: Math.floor(runtime / 1000),
      programName: processInfo.programName
    });
  } else {
    res.json({ running: false });
  }
});

app.post('/api/programs/:id/start', async (req, res) => {
  const { id } = req.params;
  const program = programs.find(p => p.id === id);

  if (!program) {
    return res.status(404).json({ error: 'Program not found' });
  }

  if (runningProcesses.has(id)) {
    return res.status(400).json({ error: 'Program already running' });
  }

  try {
    const programPath = path.join(__dirname, '..', program.directory);

    // Send initial status
    io.emit('program-output', {
      programId: id,
      type: 'info',
      data: `🚀 Starting ${program.name}...\n`
    });

    // Check if program directory exists
    if (!await fs.pathExists(programPath)) {
      throw new Error(`Program directory not found: ${program.directory}`);
    }

    // Check required files
    if (program.requiresFiles) {
      io.emit('program-output', {
        programId: id,
        type: 'info',
        data: `📋 Checking required files for ${program.name}...\n`
      });

      const fileCheck = await checkRequiredFiles(programPath, program.requiresFiles);

      if (!fileCheck.allPresent) {
        const missingFilesStr = fileCheck.missingFiles.join(', ');
        throw new Error(`Missing required files: ${missingFilesStr}. Please create these files in the program directory.`);
      } else {
        io.emit('program-output', {
          programId: id,
          type: 'success',
          data: `✅ All required files present: ${program.requiresFiles.join(', ')}\n`
        });
      }
    }

    // Check and install dependencies if needed
    if (program.hasPackageJson) {
      io.emit('program-output', {
        programId: id,
        type: 'info',
        data: `📦 Checking dependencies for ${program.name}...\n`
      });

      const depStatus = await checkDependencies(programPath);

      if (!depStatus.installed) {
        io.emit('program-output', {
          programId: id,
          type: 'info',
          data: `⬇️ Installing dependencies for ${program.name}...\n`
        });

        try {
          await installDependencies(programPath, id);
          io.emit('program-output', {
            programId: id,
            type: 'success',
            data: `✅ Dependencies installed successfully!\n`
          });
        } catch (installError) {
          throw new Error(`Failed to install dependencies: ${installError.error}`);
        }
      } else {
        io.emit('program-output', {
          programId: id,
          type: 'success',
          data: `✅ Dependencies already installed\n`
        });
      }
    }

    // Build project if required
    if (program.buildRequired && program.buildCommand) {
      io.emit('program-output', {
        programId: id,
        type: 'info',
        data: `🔨 Building ${program.name}...\n`
      });

      try {
        await buildProject(programPath, id, program.buildCommand);
        io.emit('program-output', {
          programId: id,
          type: 'success',
          data: `✅ Build completed successfully!\n`
        });
      } catch (buildError) {
        throw new Error(`Failed to build project: ${buildError.error}`);
      }
    }

    // Start the actual program
    io.emit('program-output', {
      programId: id,
      type: 'info',
      data: `🎯 Launching ${program.name} with command: ${program.command} ${program.args.join(' ')}\n`
    });

    const childProcess = spawn(program.command, program.args, {
      cwd: programPath,
      stdio: ['pipe', 'pipe', 'pipe'],
      env: {
        ...process.env,
        FORCE_COLOR: '1',
        NODE_ENV: 'production',
        CI: 'false',
        TERM: 'xterm-256color'
      },
      shell: false
    });

    // Store process with additional metadata
    runningProcesses.set(id, {
      process: childProcess,
      startTime: new Date(),
      programName: program.name,
      programId: id
    });

    // Handle process output
    childProcess.stdout.on('data', (data) => {
      io.emit('program-output', {
        programId: id,
        type: 'stdout',
        data: data.toString()
      });
    });

    childProcess.stderr.on('data', (data) => {
      io.emit('program-output', {
        programId: id,
        type: 'stderr',
        data: data.toString()
      });
    });

    childProcess.on('close', (code) => {
      const processInfo = runningProcesses.get(id);
      const runtime = processInfo ? new Date() - processInfo.startTime : 0;

      runningProcesses.delete(id);
      io.emit('program-status', {
        programId: id,
        status: 'stopped',
        exitCode: code,
        runtime: Math.floor(runtime / 1000) // seconds
      });

      io.emit('program-output', {
        programId: id,
        type: code === 0 ? 'success' : 'error',
        data: `\n🔴 Program ${program.name} stopped with exit code: ${code} (ran for ${Math.floor(runtime / 1000)}s)\n`
      });
    });

    childProcess.on('error', (error) => {
      runningProcesses.delete(id);
      io.emit('program-output', {
        programId: id,
        type: 'error',
        data: `❌ Error starting program: ${error.message}\n`
      });

      io.emit('program-status', {
        programId: id,
        status: 'error',
        error: error.message
      });
    });

    // Send success response
    res.json({ success: true, message: 'Program started successfully' });

    // Emit status update
    io.emit('program-status', {
      programId: id,
      status: 'running'
    });

  } catch (error) {
    io.emit('program-output', {
      programId: id,
      type: 'error',
      data: `❌ Failed to start ${program.name}: ${error.message}\n`
    });

    res.status(500).json({ error: error.message });
  }
});

app.post('/api/programs/:id/stop', (req, res) => {
  const { id } = req.params;
  const processInfo = runningProcesses.get(id);

  if (!processInfo) {
    return res.status(400).json({ error: 'Program not running' });
  }

  processInfo.process.kill('SIGTERM');

  // Give it a moment, then force kill if needed
  setTimeout(() => {
    if (runningProcesses.has(id)) {
      processInfo.process.kill('SIGKILL');
      runningProcesses.delete(id);
    }
  }, 5000);

  res.json({ success: true, message: 'Program stop signal sent' });
});

app.post('/api/programs/:id/input', (req, res) => {
  const { id } = req.params;
  const { input } = req.body;
  const processInfo = runningProcesses.get(id);

  if (!processInfo) {
    return res.status(400).json({ error: 'Program not running' });
  }

  if (input === undefined || input === null) {
    return res.status(400).json({ error: 'Input is required' });
  }

  try {
    processInfo.process.stdin.write(input);
    res.json({ success: true, message: 'Input sent to program' });
  } catch (error) {
    console.error(`Failed to send input to ${id}:`, error);
    res.status(500).json({ error: 'Failed to send input to program' });
  }
});

// File management endpoints
app.get('/api/programs/:id/files', async (req, res) => {
  const { id } = req.params;
  const program = programs.find(p => p.id === id);

  if (!program) {
    return res.status(404).json({ error: 'Program not found' });
  }

  try {
    const programPath = path.join(__dirname, '..', program.directory);
    const files = await fs.readdir(programPath);
    const fileList = [];

    for (const file of files) {
      const filePath = path.join(programPath, file);
      const stats = await fs.stat(filePath);

      if (stats.isFile() && (file.endsWith('.js') || file.endsWith('.mjs') || file.endsWith('.json') || file.endsWith('.txt'))) {
        fileList.push({
          name: file,
          size: stats.size,
          modified: stats.mtime
        });
      }
    }

    res.json(fileList);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

app.get('/api/programs/:id/files/:filename', async (req, res) => {
  const { id, filename } = req.params;
  const program = programs.find(p => p.id === id);

  if (!program) {
    return res.status(404).json({ error: 'Program not found' });
  }

  try {
    const filePath = path.join(__dirname, '..', program.directory, filename);
    const content = await fs.readFile(filePath, 'utf8');
    res.json({ content });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

app.post('/api/programs/:id/files/:filename', async (req, res) => {
  const { id, filename } = req.params;
  const { content } = req.body;
  const program = programs.find(p => p.id === id);

  if (!program) {
    return res.status(404).json({ error: 'Program not found' });
  }

  try {
    const filePath = path.join(__dirname, '..', program.directory, filename);
    await fs.writeFile(filePath, content, 'utf8');
    res.json({ success: true, message: 'File saved' });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

app.post('/api/programs/:id/create-template/:filename', async (req, res) => {
  const { id, filename } = req.params;
  const program = programs.find(p => p.id === id);

  if (!program) {
    return res.status(404).json({ error: 'Program not found' });
  }

  try {
    const programPath = path.join(__dirname, '..', program.directory);
    const content = await createTemplateFile(programPath, filename, id);
    res.json({ success: true, message: 'Template file created', content });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Socket.io connection handling
io.on('connection', (socket) => {
  console.log('Client connected:', socket.id);

  socket.on('disconnect', () => {
    console.log('Client disconnected:', socket.id);
  });
});

const PORT = process.env.PORT || 3000;
server.listen(PORT, () => {
  console.log('🚀 Testnet Program Dashboard Started!');
  console.log(`📊 Server running on: http://localhost:${PORT}`);
  console.log(`📋 Managing ${programs.length} programs:`);

  // List all programs
  programs.forEach((program, index) => {
    console.log(`   ${index + 1}. ${program.name} (${program.type})`);
  });

  console.log('\n✨ Features:');
  console.log('   • Auto dependency installation');
  console.log('   • Real-time console output');
  console.log('   • Interactive program input');
  console.log('   • File editing capabilities');
  console.log('   • Build process automation');

  console.log('\n🌐 Open your browser and navigate to the URL above to get started!');
});
