nohup: <PERSON><PERSON><PERSON><PERSON><PERSON> masukan
🚀 Testnet Program Dashboard Started!
📊 Server running on: http://localhost:3000
📋 Managing 23 programs:
   1. 0G Network (blockchain)
   2. 0G Storage (storage)
   3. Aster AutoBot NTE (bot)
   4. Byte Protocol (protocol)
   5. Coinsif Referral (referral)
   6. Enso Finance (defi)
   7. <PERSON><PERSON><PERSON> (bot)
   8. Huddle Protocol (deployment)
   9. Inco Network (blockchain)
   10. INFTS Protocol (nft)
   11. Kite AI Auto Bot (protocol)
   12. Maitrix Auto Bot (blockchain)
   13. Merak SUI DEX Bot (defi)
   14. Pharos Protocol (protocol)
   15. Pharos Original (protocol)
   16. R2 Protocol (protocol)
   17. R2 NTE (protocol)
   18. Rise Protocol (protocol)
   19. Rome EVM Deployer (deployment)
   20. Shift Protocol (protocol)
   21. T1 Auto Bridge (protocol)
   22. TradeGPT Auto Bot (trading)
   23. Union Auto Bot (protocol)

✨ Features:
   • Auto dependency installation
   • Real-time console output
   • Interactive program input
   • File editing capabilities
   • Build process automation

🌐 Open your browser and navigate to the URL above to get started!
Client connected: 4GaaJ0fqp5jOcWzoAAAB
[INPUT DEBUG] Program: 0g, Input received: "
", Type: string, Length: 1
[INPUT DEBUG] Sending to stdin: "
" (1 chars)
[INPUT DEBUG] Processed input: "
" (2 chars)
[INPUT DEBUG] Program: 0g, Input received: "
", Type: string, Length: 1
[INPUT DEBUG] Sending to stdin: "
" (1 chars)
[INPUT DEBUG] Processed input: "
" (2 chars)
