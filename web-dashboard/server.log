nohup: <PERSON><PERSON><PERSON><PERSON><PERSON> masukan
🚀 Testnet Program Dashboard Started!
📊 Server running on: http://localhost:3000
📋 Managing 23 programs:
   1. 0G Network (blockchain)
   2. 0G Storage (storage)
   3. Aster AutoBot NTE (bot)
   4. Byte Protocol (protocol)
   5. Coinsif Referral (referral)
   6. Enso Finance (defi)
   7. <PERSON><PERSON><PERSON> (bot)
   8. Huddle Protocol (deployment)
   9. Inco Network (blockchain)
   10. INFTS Protocol (nft)
   11. Kite AI Auto Bot (protocol)
   12. Maitrix Auto Bot (blockchain)
   13. Merak SUI DEX Bot (defi)
   14. Pharos Protocol (protocol)
   15. Pharos Original (protocol)
   16. R2 Protocol (protocol)
   17. R2 NTE (protocol)
   18. Rise Protocol (protocol)
   19. Rome EVM Deployer (deployment)
   20. Shift Protocol (protocol)
   21. T1 Auto Bridge (protocol)
   22. TradeGPT Auto Bot (trading)
   23. Union Auto Bot (protocol)

✨ Features:
   • Auto dependency installation
   • Real-time console output
   • Interactive program input
   • File editing capabilities
   • Build process automation

🌐 Open your browser and navigate to the URL above to get started!
Client connected: D9nom0ZUCPP4lOUMAAAB
[PATH DEBUG] Program: Maitrix Auto Bot, Directory: Maitrix, Full path: /home/<USER>/testnet (Salin)/Maitrix
[START DEBUG] Program: Maitrix Auto Bot, Directory: Maitrix, Full path: /home/<USER>/testnet (Salin)/Maitrix
[INPUT DEBUG] Program: maitrix, Input received: "[B", Type: string, Length: 3
[INPUT DEBUG] Sending to stdin: "[B" (3 chars)
[INPUT DEBUG] Processed input: "[B
" (4 chars)
[INPUT DEBUG] Input bytes: <Buffer 1b 5b 42 0a>
[INPUT DEBUG] Write success: true
[INPUT DEBUG] Program: maitrix, Input received: "[B", Type: string, Length: 3
[INPUT DEBUG] Sending to stdin: "[B" (3 chars)
[INPUT DEBUG] Processed input: "[B
" (4 chars)
[INPUT DEBUG] Input bytes: <Buffer 1b 5b 42 0a>
[INPUT DEBUG] Write success: true
[INPUT DEBUG] Program: maitrix, Input received: "[D", Type: string, Length: 3
[INPUT DEBUG] Sending to stdin: "[D" (3 chars)
[INPUT DEBUG] Processed input: "[D
" (4 chars)
[INPUT DEBUG] Input bytes: <Buffer 1b 5b 44 0a>
[INPUT DEBUG] Write success: true
[INPUT DEBUG] Program: maitrix, Input received: "[A", Type: string, Length: 3
[INPUT DEBUG] Sending to stdin: "[A" (3 chars)
[INPUT DEBUG] Processed input: "[A
" (4 chars)
[INPUT DEBUG] Input bytes: <Buffer 1b 5b 41 0a>
[INPUT DEBUG] Write success: true
[INPUT DEBUG] Program: maitrix, Input received: "[D", Type: string, Length: 3
[INPUT DEBUG] Sending to stdin: "[D" (3 chars)
[INPUT DEBUG] Processed input: "[D
" (4 chars)
[INPUT DEBUG] Input bytes: <Buffer 1b 5b 44 0a>
[INPUT DEBUG] Write success: true
[INPUT DEBUG] Program: maitrix, Input received: "[C", Type: string, Length: 3
[INPUT DEBUG] Sending to stdin: "[C" (3 chars)
[INPUT DEBUG] Processed input: "[C
" (4 chars)
[INPUT DEBUG] Input bytes: <Buffer 1b 5b 43 0a>
[INPUT DEBUG] Write success: true
[INPUT DEBUG] Program: maitrix, Input received: "[A", Type: string, Length: 3
[INPUT DEBUG] Sending to stdin: "[A" (3 chars)
[INPUT DEBUG] Processed input: "[A
" (4 chars)
[INPUT DEBUG] Input bytes: <Buffer 1b 5b 41 0a>
[INPUT DEBUG] Write success: true
[INPUT DEBUG] Program: maitrix, Input received: "[B", Type: string, Length: 3
[INPUT DEBUG] Sending to stdin: "[B" (3 chars)
[INPUT DEBUG] Processed input: "[B
" (4 chars)
[INPUT DEBUG] Input bytes: <Buffer 1b 5b 42 0a>
[INPUT DEBUG] Write success: true
