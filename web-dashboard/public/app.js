class TestnetDashboard {
    constructor() {
        this.programs = [];
        this.currentProgram = null;
        this.socket = io();
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.setupSocketListeners();
        this.loadPrograms();
    }

    setupEventListeners() {
        // Modal controls
        document.getElementById('modal-close').addEventListener('click', () => {
            this.closeModal();
        });

        // Tab switching
        document.querySelectorAll('.tab-button').forEach(button => {
            button.addEventListener('click', (e) => {
                this.switchTab(e.target.dataset.tab);
            });
        });

        // Program controls
        document.getElementById('start-program').addEventListener('click', () => {
            this.startProgram();
        });

        document.getElementById('stop-program').addEventListener('click', () => {
            this.stopProgram();
        });

        document.getElementById('clear-console').addEventListener('click', () => {
            this.clearConsole();
        });

        // File management
        document.getElementById('refresh-files').addEventListener('click', () => {
            this.loadProgramFiles();
        });

        document.getElementById('file-selector').addEventListener('change', (e) => {
            this.loadFileContent(e.target.value);
        });

        document.getElementById('save-file').addEventListener('click', () => {
            this.saveFile();
        });

        // Console input
        document.getElementById('send-input').addEventListener('click', () => {
            this.sendInput();
        });

        document.getElementById('console-input-field').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.sendInput();
            }
        });

        // Template creation
        document.getElementById('create-template').addEventListener('click', () => {
            this.toggleTemplateOptions();
        });

        document.querySelectorAll('.template-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.createTemplate(e.target.dataset.template);
            });
        });

        // Arrow key controls
        document.getElementById('arrow-up').addEventListener('click', () => {
            this.sendSpecialKey('ArrowUp');
        });

        document.getElementById('arrow-down').addEventListener('click', () => {
            this.sendSpecialKey('ArrowDown');
        });

        document.getElementById('arrow-left').addEventListener('click', () => {
            this.sendSpecialKey('ArrowLeft');
        });

        document.getElementById('arrow-right').addEventListener('click', () => {
            this.sendSpecialKey('ArrowRight');
        });

        document.getElementById('enter-key').addEventListener('click', () => {
            this.sendSpecialKey('Enter');
        });

        document.getElementById('escape-key').addEventListener('click', () => {
            this.sendSpecialKey('Escape');
        });

        // Search and filter
        document.getElementById('search-input').addEventListener('input', (e) => {
            this.filterPrograms();
        });

        document.getElementById('type-filter').addEventListener('change', () => {
            this.filterPrograms();
        });

        // Close modal when clicking outside
        window.addEventListener('click', (e) => {
            const modal = document.getElementById('program-modal');
            if (e.target === modal) {
                this.closeModal();
            }
        });
    }

    setupSocketListeners() {
        this.socket.on('program-output', (data) => {
            this.appendConsoleOutput(data);
        });

        this.socket.on('program-status', (data) => {
            this.updateProgramStatus(data);
        });
    }

    async loadPrograms() {
        this.showLoading(true);
        try {
            const response = await fetch('/api/programs');
            this.programs = await response.json();
            this.renderPrograms();
            this.updateStats();
        } catch (error) {
            console.error('Error loading programs:', error);
            this.showError('Failed to load programs');
        } finally {
            this.showLoading(false);
        }
    }

    renderPrograms() {
        const grid = document.getElementById('programs-grid');
        grid.innerHTML = '';

        const filteredPrograms = this.getFilteredPrograms();

        filteredPrograms.forEach(program => {
            const card = this.createProgramCard(program);
            grid.appendChild(card);
        });
    }

    createProgramCard(program) {
        const card = document.createElement('div');
        card.className = 'program-card';
        card.innerHTML = `
            <div class="program-header">
                <div>
                    <div class="program-title">${program.name}</div>
                    <span class="program-type">${program.type}</span>
                </div>
                <span class="status stopped" id="status-${program.id}">Stopped</span>
            </div>
            <div class="program-description">${program.description}</div>
            <div class="program-actions">
                <button class="btn btn-primary" onclick="dashboard.openProgram('${program.id}')">
                    <i class="fas fa-cog"></i> Manage
                </button>
                <button class="btn btn-success" onclick="dashboard.quickStart('${program.id}')" id="quick-start-${program.id}">
                    <i class="fas fa-play"></i> Quick Start
                </button>
            </div>
        `;

        // Check program status
        this.checkProgramStatus(program.id);

        return card;
    }

    async checkProgramStatus(programId) {
        try {
            const response = await fetch(`/api/programs/${programId}/status`);
            const data = await response.json();
            this.updateProgramCardStatus(programId, data.running);
        } catch (error) {
            console.error('Error checking program status:', error);
        }
    }

    updateProgramCardStatus(programId, isRunning) {
        const statusElement = document.getElementById(`status-${programId}`);
        const quickStartBtn = document.getElementById(`quick-start-${programId}`);
        const card = statusElement.closest('.program-card');

        if (statusElement) {
            statusElement.textContent = isRunning ? 'Running' : 'Stopped';
            statusElement.className = `status ${isRunning ? 'running' : 'stopped'}`;
        }

        if (quickStartBtn) {
            quickStartBtn.innerHTML = isRunning ?
                '<i class="fas fa-stop"></i> Stop' :
                '<i class="fas fa-play"></i> Quick Start';
            quickStartBtn.className = `btn ${isRunning ? 'btn-danger' : 'btn-success'}`;
            quickStartBtn.onclick = isRunning ?
                () => this.quickStop(programId) :
                () => this.quickStart(programId);
        }

        if (card) {
            card.classList.toggle('running', isRunning);
        }
    }

    getFilteredPrograms() {
        const searchTerm = document.getElementById('search-input').value.toLowerCase();
        const typeFilter = document.getElementById('type-filter').value;

        return this.programs.filter(program => {
            const matchesSearch = program.name.toLowerCase().includes(searchTerm) ||
                                program.description.toLowerCase().includes(searchTerm);
            const matchesType = !typeFilter || program.type === typeFilter;
            return matchesSearch && matchesType;
        });
    }

    filterPrograms() {
        this.renderPrograms();
        this.updateStats();
    }

    updateStats() {
        const filteredPrograms = this.getFilteredPrograms();
        document.getElementById('total-programs').textContent = filteredPrograms.length;

        // Count running programs (this would need to be updated with real status)
        let runningCount = 0;
        filteredPrograms.forEach(program => {
            const statusElement = document.getElementById(`status-${program.id}`);
            if (statusElement && statusElement.textContent === 'Running') {
                runningCount++;
            }
        });

        document.getElementById('running-programs').textContent = runningCount;
        document.getElementById('stopped-programs').textContent = filteredPrograms.length - runningCount;
    }

    async quickStart(programId) {
        try {
            const response = await fetch(`/api/programs/${programId}/start`, {
                method: 'POST'
            });

            if (response.ok) {
                this.updateProgramCardStatus(programId, true);
                this.showSuccess('Program started successfully');
            } else {
                const error = await response.json();
                this.showError(error.error || 'Failed to start program');
            }
        } catch (error) {
            console.error('Error starting program:', error);
            this.showError('Failed to start program');
        }
    }

    async quickStop(programId) {
        try {
            const response = await fetch(`/api/programs/${programId}/stop`, {
                method: 'POST'
            });

            if (response.ok) {
                this.updateProgramCardStatus(programId, false);
                this.showSuccess('Program stopped successfully');
            } else {
                const error = await response.json();
                this.showError(error.error || 'Failed to stop program');
            }
        } catch (error) {
            console.error('Error stopping program:', error);
            this.showError('Failed to stop program');
        }
    }

    openProgram(programId) {
        this.currentProgram = this.programs.find(p => p.id === programId);
        if (!this.currentProgram) return;

        document.getElementById('modal-title').textContent = this.currentProgram.name;
        document.getElementById('program-modal').style.display = 'block';

        this.switchTab('console');
        this.loadProgramFiles();
        this.checkProgramStatus(programId);

        // Load console history for this program
        this.loadConsoleHistory(programId);
    }

    loadConsoleHistory(programId) {
        const consoleOutput = document.getElementById('console-output');
        consoleOutput.innerHTML = '';

        if (this.programOutputs && this.programOutputs.has(programId)) {
            const outputs = this.programOutputs.get(programId);
            outputs.forEach(data => {
                const outputLine = document.createElement('div');
                outputLine.className = `output-line ${data.type}`;
                outputLine.setAttribute('data-timestamp', data.timestamp || new Date().toISOString());

                // Set colors based on output type
                switch(data.type) {
                    case 'info':
                        outputLine.style.color = '#61dafb';
                        break;
                    case 'success':
                        outputLine.style.color = '#28a745';
                        break;
                    case 'error':
                        outputLine.style.color = '#dc3545';
                        break;
                    case 'stderr':
                        outputLine.style.color = '#ffc107';
                        break;
                    case 'install':
                    case 'install-error':
                        outputLine.style.color = '#17a2b8';
                        break;
                    case 'build':
                    case 'build-error':
                        outputLine.style.color = '#6f42c1';
                        break;
                    case 'input':
                        outputLine.style.color = '#20c997';
                        outputLine.style.fontWeight = 'bold';
                        break;
                    default:
                        outputLine.style.color = '#fff';
                }

                const cleanedData = this.processAnsiCodes(data.data);
                outputLine.innerHTML = cleanedData;
                consoleOutput.appendChild(outputLine);
            });

            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        }
    }

    processAnsiCodes(text) {
        if (!text) return '';

        // Convert ANSI escape codes to HTML
        return text
            .replace(/\x1b\[0m/g, '</span>') // Reset
            .replace(/\x1b\[1m/g, '<span style="font-weight: bold;">') // Bold
            .replace(/\x1b\[31m/g, '<span style="color: #ff6b6b;">') // Red
            .replace(/\x1b\[32m/g, '<span style="color: #51cf66;">') // Green
            .replace(/\x1b\[33m/g, '<span style="color: #ffd43b;">') // Yellow
            .replace(/\x1b\[34m/g, '<span style="color: #339af0;">') // Blue
            .replace(/\x1b\[35m/g, '<span style="color: #cc5de8;">') // Magenta
            .replace(/\x1b\[36m/g, '<span style="color: #22b8cf;">') // Cyan
            .replace(/\x1b\[37m/g, '<span style="color: #adb5bd;">') // White
            .replace(/\x1b\[90m/g, '<span style="color: #6c757d;">') // Bright Black (Gray)
            .replace(/\x1b\[91m/g, '<span style="color: #ff8787;">') // Bright Red
            .replace(/\x1b\[92m/g, '<span style="color: #8ce99a;">') // Bright Green
            .replace(/\x1b\[93m/g, '<span style="color: #ffe066;">') // Bright Yellow
            .replace(/\x1b\[94m/g, '<span style="color: #74c0fc;">') // Bright Blue
            .replace(/\x1b\[95m/g, '<span style="color: #d0bfff;">') // Bright Magenta
            .replace(/\x1b\[96m/g, '<span style="color: #66d9ef;">') // Bright Cyan
            .replace(/\x1b\[97m/g, '<span style="color: #f8f9fa;">') // Bright White
            .replace(/\x1b\[[0-9;]*m/g, '') // Remove other escape codes
            .replace(/\r\n/g, '\n')
            .replace(/\r/g, '\n');
    }

    closeModal() {
        document.getElementById('program-modal').style.display = 'none';
        this.currentProgram = null;
    }

    switchTab(tabName) {
        // Update tab buttons
        document.querySelectorAll('.tab-button').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');

        // Update tab content
        document.querySelectorAll('.tab-pane').forEach(pane => {
            pane.classList.remove('active');
        });
        document.getElementById(`${tabName}-tab`).classList.add('active');
    }

    async startProgram() {
        if (!this.currentProgram) return;

        try {
            const response = await fetch(`/api/programs/${this.currentProgram.id}/start`, {
                method: 'POST'
            });

            if (response.ok) {
                this.updateModalProgramStatus(true);
                this.updateProgramCardStatus(this.currentProgram.id, true);
                this.showSuccess('Program started successfully');
            } else {
                const error = await response.json();
                this.showError(error.error || 'Failed to start program');
            }
        } catch (error) {
            console.error('Error starting program:', error);
            this.showError('Failed to start program');
        }
    }

    async stopProgram() {
        if (!this.currentProgram) return;

        try {
            const response = await fetch(`/api/programs/${this.currentProgram.id}/stop`, {
                method: 'POST'
            });

            if (response.ok) {
                this.updateModalProgramStatus(false);
                this.updateProgramCardStatus(this.currentProgram.id, false);
                this.showSuccess('Program stopped successfully');
            } else {
                const error = await response.json();
                this.showError(error.error || 'Failed to stop program');
            }
        } catch (error) {
            console.error('Error stopping program:', error);
            this.showError('Failed to stop program');
        }
    }

    updateModalProgramStatus(isRunning) {
        const statusElement = document.getElementById('program-status');
        const startBtn = document.getElementById('start-program');
        const stopBtn = document.getElementById('stop-program');
        const inputField = document.getElementById('console-input-field');
        const sendBtn = document.getElementById('send-input');

        // Arrow key buttons
        const arrowBtns = [
            'arrow-up', 'arrow-down', 'arrow-left', 'arrow-right',
            'enter-key', 'escape-key'
        ];

        statusElement.textContent = isRunning ? 'Running' : 'Stopped';
        statusElement.className = `status ${isRunning ? 'running' : 'stopped'}`;

        startBtn.disabled = isRunning;
        stopBtn.disabled = !isRunning;
        inputField.disabled = !isRunning;
        sendBtn.disabled = !isRunning;

        // Enable/disable arrow key buttons
        arrowBtns.forEach(btnId => {
            const btn = document.getElementById(btnId);
            if (btn) {
                btn.disabled = !isRunning;
            }
        });
    }

    sendSpecialKey(keyName) {
        if (!this.currentProgram) return;

        let keyCode = '';
        switch(keyName) {
            case 'ArrowUp':
                keyCode = '\x1b[A';
                break;
            case 'ArrowDown':
                keyCode = '\x1b[B';
                break;
            case 'ArrowRight':
                keyCode = '\x1b[C';
                break;
            case 'ArrowLeft':
                keyCode = '\x1b[D';
                break;
            case 'Enter':
                keyCode = '\r';
                break;
            case 'Escape':
                keyCode = '\x1b';
                break;
            default:
                return;
        }

        // Send special key to server
        fetch(`/api/programs/${this.currentProgram.id}/input`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ input: keyCode })
        }).then(response => {
            if (response.ok) {
                // Show key press in console
                this.appendConsoleOutput({
                    programId: this.currentProgram.id,
                    type: 'input',
                    data: `[${keyName}]\n`
                });
            } else {
                this.showError(`Failed to send ${keyName} key`);
            }
        }).catch(error => {
            console.error('Error sending special key:', error);
            this.showError(`Failed to send ${keyName} key`);
        });
    }

    sendInput() {
        const inputField = document.getElementById('console-input-field');
        const input = inputField.value.trim();

        if (!input || !this.currentProgram) return;

        // Send input to server
        fetch(`/api/programs/${this.currentProgram.id}/input`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ input: input + '\n' })
        }).then(response => {
            if (response.ok) {
                // Show input in console
                this.appendConsoleOutput({
                    programId: this.currentProgram.id,
                    type: 'input',
                    data: `> ${input}\n`
                });
                inputField.value = '';
            } else {
                this.showError('Failed to send input to program');
            }
        }).catch(error => {
            console.error('Error sending input:', error);
            this.showError('Failed to send input to program');
        });
    }

    clearConsole() {
        document.getElementById('console-output').innerHTML = '';
    }

    appendConsoleOutput(data) {
        // Store output for all programs, not just current one
        if (!this.programOutputs) {
            this.programOutputs = new Map();
        }

        if (!this.programOutputs.has(data.programId)) {
            this.programOutputs.set(data.programId, []);
        }

        // Store the output data
        this.programOutputs.get(data.programId).push(data);

        // Only display if this is the currently viewed program
        if (!this.currentProgram || data.programId !== this.currentProgram.id) return;

        const consoleOutput = document.getElementById('console-output');
        const outputLine = document.createElement('div');
        outputLine.className = `output-line ${data.type}`;
        outputLine.setAttribute('data-timestamp', new Date().toISOString());

        // Set colors based on output type
        switch(data.type) {
            case 'info':
                outputLine.style.color = '#61dafb';
                break;
            case 'success':
                outputLine.style.color = '#28a745';
                break;
            case 'error':
                outputLine.style.color = '#dc3545';
                break;
            case 'stderr':
                outputLine.style.color = '#ffc107';
                break;
            case 'install':
            case 'install-error':
                outputLine.style.color = '#17a2b8';
                break;
            case 'build':
            case 'build-error':
                outputLine.style.color = '#6f42c1';
                break;
            case 'input':
                outputLine.style.color = '#20c997';
                outputLine.style.fontWeight = 'bold';
                break;
            default:
                outputLine.style.color = '#fff';
        }

        // Handle ANSI escape codes for better CLI display
        const cleanedData = this.processAnsiCodes(data.data);
        outputLine.innerHTML = cleanedData;

        consoleOutput.appendChild(outputLine);
        consoleOutput.scrollTop = consoleOutput.scrollHeight;

        // Limit console output to prevent memory issues
        const maxLines = 1000;
        while (consoleOutput.children.length > maxLines) {
            consoleOutput.removeChild(consoleOutput.firstChild);
        }
    }

    async loadProgramFiles() {
        if (!this.currentProgram) return;

        try {
            const response = await fetch(`/api/programs/${this.currentProgram.id}/files`);
            const files = await response.json();

            this.renderFilesList(files);
            this.populateFileSelector(files);
        } catch (error) {
            console.error('Error loading files:', error);
            this.showError('Failed to load program files');
        }
    }

    renderFilesList(files) {
        const filesList = document.getElementById('files-list');
        filesList.innerHTML = '';

        files.forEach(file => {
            const fileItem = document.createElement('div');
            fileItem.className = 'file-item';
            fileItem.innerHTML = `
                <div class="file-info">
                    <i class="fas fa-file-code file-icon"></i>
                    <span>${file.name}</span>
                </div>
                <div class="file-meta">
                    <small>${this.formatFileSize(file.size)} • ${this.formatDate(file.modified)}</small>
                </div>
            `;

            fileItem.addEventListener('click', () => {
                document.getElementById('file-selector').value = file.name;
                this.loadFileContent(file.name);
                this.switchTab('config');
            });

            filesList.appendChild(fileItem);
        });
    }

    populateFileSelector(files) {
        const selector = document.getElementById('file-selector');
        selector.innerHTML = '<option value="">Select a file to edit</option>';

        files.forEach(file => {
            const option = document.createElement('option');
            option.value = file.name;
            option.textContent = file.name;
            selector.appendChild(option);
        });
    }

    async loadFileContent(filename) {
        if (!filename || !this.currentProgram) return;

        try {
            const response = await fetch(`/api/programs/${this.currentProgram.id}/files/${filename}`);
            const data = await response.json();

            document.getElementById('file-editor').value = data.content;
        } catch (error) {
            console.error('Error loading file content:', error);
            this.showError('Failed to load file content');
        }
    }

    async saveFile() {
        const filename = document.getElementById('file-selector').value;
        const content = document.getElementById('file-editor').value;

        if (!filename || !this.currentProgram) {
            this.showError('Please select a file to save');
            return;
        }

        try {
            const response = await fetch(`/api/programs/${this.currentProgram.id}/files/${filename}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ content })
            });

            if (response.ok) {
                this.showSuccess('File saved successfully');
            } else {
                const error = await response.json();
                this.showError(error.error || 'Failed to save file');
            }
        } catch (error) {
            console.error('Error saving file:', error);
            this.showError('Failed to save file');
        }
    }

    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    formatDate(dateString) {
        return new Date(dateString).toLocaleDateString();
    }

    showLoading(show) {
        document.getElementById('loading-overlay').style.display = show ? 'flex' : 'none';
    }

    showSuccess(message) {
        this.showNotification(message, 'success');
    }

    showError(message) {
        this.showNotification(message, 'error');
    }

    showNotification(message, type) {
        // Simple notification - you could enhance this with a proper notification library
        const notification = document.createElement('div');
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 8px;
            color: white;
            font-weight: 500;
            z-index: 10000;
            background: ${type === 'success' ? '#28a745' : '#dc3545'};
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        `;
        notification.textContent = message;

        document.body.appendChild(notification);

        setTimeout(() => {
            notification.remove();
        }, 3000);
    }

    updateProgramStatus(data) {
        this.updateProgramCardStatus(data.programId, data.status === 'running');

        if (this.currentProgram && this.currentProgram.id === data.programId) {
            this.updateModalProgramStatus(data.status === 'running');
        }
    }

    toggleTemplateOptions() {
        const templateOptions = document.getElementById('template-options');
        const isVisible = templateOptions.style.display !== 'none';
        templateOptions.style.display = isVisible ? 'none' : 'block';
    }

    async createTemplate(templateName) {
        if (!this.currentProgram) return;

        try {
            const response = await fetch(`/api/programs/${this.currentProgram.id}/create-template/${templateName}`, {
                method: 'POST'
            });

            if (response.ok) {
                const data = await response.json();

                // Update file selector and load the new file
                await this.loadProgramFiles();
                document.getElementById('file-selector').value = templateName;
                document.getElementById('file-editor').value = data.content;

                // Hide template options
                document.getElementById('template-options').style.display = 'none';

                this.showSuccess(`Template file ${templateName} created successfully!`);
            } else {
                const error = await response.json();
                this.showError(error.error || 'Failed to create template file');
            }
        } catch (error) {
            console.error('Error creating template:', error);
            this.showError('Failed to create template file');
        }
    }
}

// Initialize dashboard when page loads
const dashboard = new TestnetDashboard();
