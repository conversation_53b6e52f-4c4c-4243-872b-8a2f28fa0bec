class TestnetDashboard {
    constructor() {
        this.programs = [];
        this.currentProgram = null;
        this.socket = io();
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.setupSocketListeners();
        this.loadPrograms();
    }

    setupEventListeners() {
        // Modal controls
        document.getElementById('modal-close').addEventListener('click', () => {
            this.closeModal();
        });

        // Tab switching
        document.querySelectorAll('.tab-button').forEach(button => {
            button.addEventListener('click', (e) => {
                this.switchTab(e.target.dataset.tab);
            });
        });

        // Program controls
        document.getElementById('start-program').addEventListener('click', () => {
            this.startProgram();
        });

        document.getElementById('stop-program').addEventListener('click', () => {
            this.stopProgram();
        });

        document.getElementById('clear-console').addEventListener('click', () => {
            this.clearConsole();
        });

        // File management
        document.getElementById('refresh-files').addEventListener('click', () => {
            this.loadProgramFiles();
        });

        document.getElementById('file-selector').addEventListener('change', (e) => {
            this.loadFileContent(e.target.value);
        });

        document.getElementById('save-file').addEventListener('click', () => {
            this.saveFile();
        });

        // Console input
        document.getElementById('send-input').addEventListener('click', () => {
            this.sendInput();
        });

        document.getElementById('console-input-field').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.sendInput();
            }
        });

        // Template creation
        document.getElementById('create-template').addEventListener('click', () => {
            this.toggleTemplateOptions();
        });

        document.querySelectorAll('.template-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.createTemplate(e.target.dataset.template);
            });
        });

        // Arrow key controls
        document.getElementById('arrow-up').addEventListener('click', () => {
            this.sendSpecialKey('ArrowUp');
        });

        document.getElementById('arrow-down').addEventListener('click', () => {
            this.sendSpecialKey('ArrowDown');
        });

        document.getElementById('arrow-left').addEventListener('click', () => {
            this.sendSpecialKey('ArrowLeft');
        });

        document.getElementById('arrow-right').addEventListener('click', () => {
            this.sendSpecialKey('ArrowRight');
        });

        document.getElementById('enter-key').addEventListener('click', () => {
            this.sendSpecialKey('Enter');
        });

        document.getElementById('escape-key').addEventListener('click', () => {
            this.sendSpecialKey('Escape');
        });

        document.getElementById('ctrl-c').addEventListener('click', () => {
            this.sendSpecialKey('CtrlC');
        });

        // Global keyboard event listener for the entire modal
        document.addEventListener('keydown', (e) => {
            if (this.currentProgram && document.getElementById('program-modal').style.display === 'block') {
                this.handleKeyboardInput(e);
            }
        });

        // Search and filter
        document.getElementById('search-input').addEventListener('input', (e) => {
            this.filterPrograms();
        });

        document.getElementById('type-filter').addEventListener('change', () => {
            this.filterPrograms();
        });

        // Close modal when clicking outside
        window.addEventListener('click', (e) => {
            const modal = document.getElementById('program-modal');
            if (e.target === modal) {
                this.closeModal();
            }
        });
    }

    setupSocketListeners() {
        this.socket.on('program-output', (data) => {
            this.appendConsoleOutput(data);
        });

        this.socket.on('program-status', (data) => {
            this.updateProgramStatus(data);
        });
    }

    async loadPrograms() {
        this.showLoading(true);
        try {
            const response = await fetch('/api/programs');
            this.programs = await response.json();
            this.renderPrograms();
            this.updateStats();
        } catch (error) {
            console.error('Error loading programs:', error);
            this.showError('Failed to load programs');
        } finally {
            this.showLoading(false);
        }
    }

    renderPrograms() {
        const grid = document.getElementById('programs-grid');
        grid.innerHTML = '';

        const filteredPrograms = this.getFilteredPrograms();

        filteredPrograms.forEach(program => {
            const card = this.createProgramCard(program);
            grid.appendChild(card);
        });
    }

    createProgramCard(program) {
        const card = document.createElement('div');
        card.className = 'program-card';
        card.innerHTML = `
            <div class="program-header">
                <div>
                    <div class="program-title">${program.name}</div>
                    <span class="program-type">${program.type}</span>
                </div>
                <span class="status stopped" id="status-${program.id}">Stopped</span>
            </div>
            <div class="program-description">${program.description}</div>
            <div class="program-actions">
                <button class="btn btn-primary" onclick="dashboard.openProgram('${program.id}')">
                    <i class="fas fa-cog"></i> Manage
                </button>
                <button class="btn btn-success" onclick="dashboard.quickStart('${program.id}')" id="quick-start-${program.id}">
                    <i class="fas fa-play"></i> Quick Start
                </button>
            </div>
        `;

        // Check program status
        this.checkProgramStatus(program.id);

        return card;
    }

    async checkProgramStatus(programId) {
        try {
            const response = await fetch(`/api/programs/${programId}/status`);
            const data = await response.json();
            this.updateProgramCardStatus(programId, data.running);
        } catch (error) {
            console.error('Error checking program status:', error);
        }
    }

    updateProgramCardStatus(programId, isRunning) {
        const statusElement = document.getElementById(`status-${programId}`);
        const quickStartBtn = document.getElementById(`quick-start-${programId}`);
        const card = statusElement.closest('.program-card');

        if (statusElement) {
            statusElement.textContent = isRunning ? 'Running' : 'Stopped';
            statusElement.className = `status ${isRunning ? 'running' : 'stopped'}`;
        }

        if (quickStartBtn) {
            quickStartBtn.innerHTML = isRunning ?
                '<i class="fas fa-stop"></i> Stop' :
                '<i class="fas fa-play"></i> Quick Start';
            quickStartBtn.className = `btn ${isRunning ? 'btn-danger' : 'btn-success'}`;
            quickStartBtn.onclick = isRunning ?
                () => this.quickStop(programId) :
                () => this.quickStart(programId);
        }

        if (card) {
            card.classList.toggle('running', isRunning);
        }
    }

    getFilteredPrograms() {
        const searchTerm = document.getElementById('search-input').value.toLowerCase();
        const typeFilter = document.getElementById('type-filter').value;

        return this.programs.filter(program => {
            const matchesSearch = program.name.toLowerCase().includes(searchTerm) ||
                                program.description.toLowerCase().includes(searchTerm);
            const matchesType = !typeFilter || program.type === typeFilter;
            return matchesSearch && matchesType;
        });
    }

    filterPrograms() {
        this.renderPrograms();
        this.updateStats();
    }

    updateStats() {
        const filteredPrograms = this.getFilteredPrograms();
        document.getElementById('total-programs').textContent = filteredPrograms.length;

        // Count running programs (this would need to be updated with real status)
        let runningCount = 0;
        filteredPrograms.forEach(program => {
            const statusElement = document.getElementById(`status-${program.id}`);
            if (statusElement && statusElement.textContent === 'Running') {
                runningCount++;
            }
        });

        document.getElementById('running-programs').textContent = runningCount;
        document.getElementById('stopped-programs').textContent = filteredPrograms.length - runningCount;
    }

    async quickStart(programId) {
        try {
            const response = await fetch(`/api/programs/${programId}/start`, {
                method: 'POST'
            });

            if (response.ok) {
                this.updateProgramCardStatus(programId, true);
                this.showSuccess('Program started successfully');
            } else {
                const error = await response.json();
                this.showError(error.error || 'Failed to start program');
            }
        } catch (error) {
            console.error('Error starting program:', error);
            this.showError('Failed to start program');
        }
    }

    async quickStop(programId) {
        try {
            const response = await fetch(`/api/programs/${programId}/stop`, {
                method: 'POST'
            });

            if (response.ok) {
                this.updateProgramCardStatus(programId, false);
                this.showSuccess('Program stopped successfully');
            } else {
                const error = await response.json();
                this.showError(error.error || 'Failed to stop program');
            }
        } catch (error) {
            console.error('Error stopping program:', error);
            this.showError('Failed to stop program');
        }
    }

    openProgram(programId) {
        this.currentProgram = this.programs.find(p => p.id === programId);
        if (!this.currentProgram) return;

        document.getElementById('modal-title').textContent = this.currentProgram.name;
        document.getElementById('program-modal').style.display = 'block';

        this.switchTab('console');
        this.loadProgramFiles();
        this.checkProgramStatus(programId);

        // Load console history for this program
        this.loadConsoleHistory(programId);
    }

    loadConsoleHistory(programId) {
        const consoleOutput = document.getElementById('console-output');
        consoleOutput.innerHTML = '';

        if (this.programOutputs && this.programOutputs.has(programId)) {
            const outputs = this.programOutputs.get(programId);
            outputs.forEach(data => {
                const outputLine = document.createElement('div');
                outputLine.className = `output-line ${data.type}`;
                outputLine.setAttribute('data-timestamp', data.timestamp || new Date().toISOString());

                // Set colors based on output type
                switch(data.type) {
                    case 'info':
                        outputLine.style.color = '#61dafb';
                        break;
                    case 'success':
                        outputLine.style.color = '#28a745';
                        break;
                    case 'error':
                        outputLine.style.color = '#dc3545';
                        break;
                    case 'stderr':
                        outputLine.style.color = '#ffc107';
                        break;
                    case 'install':
                    case 'install-error':
                        outputLine.style.color = '#17a2b8';
                        break;
                    case 'build':
                    case 'build-error':
                        outputLine.style.color = '#6f42c1';
                        break;
                    case 'input':
                        outputLine.style.color = '#20c997';
                        outputLine.style.fontWeight = 'bold';
                        break;
                    default:
                        outputLine.style.color = '#fff';
                }

                const cleanedData = this.processAnsiCodes(data.data);
                outputLine.innerHTML = cleanedData;
                consoleOutput.appendChild(outputLine);
            });

            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        }
    }

    processAnsiCodes(text) {
        if (!text) return '';

        // Handle blessed terminal sequences and ANSI codes
        let processed = text
            // Handle blessed terminal control sequences
            .replace(/\x1b\[\?1049h/g, '') // Enter alternate screen
            .replace(/\x1b\[\?1049l/g, '') // Exit alternate screen
            .replace(/\x1b\[\?1h=/g, '') // Application cursor keys
            .replace(/\x1b\[\?1l>/g, '') // Normal cursor keys
            .replace(/\x1b\[1;1r/g, '') // Set scroll region
            .replace(/\x1b\[\?25l/g, '') // Hide cursor
            .replace(/\x1b\[\?25h/g, '') // Show cursor
            .replace(/\x1b\[1;1H/g, '') // Move cursor to home
            .replace(/\x1b\[H/g, '') // Move cursor to home
            .replace(/\x1b\[2J/g, '') // Clear screen
            .replace(/\x1b\[\?1000h/g, '') // Enable mouse tracking
            .replace(/\x1b\[\?1002h/g, '') // Enable button event tracking
            .replace(/\x1b\[\?1003h/g, '') // Enable any event tracking
            .replace(/\x1b\[\?1005h/g, '') // Enable UTF-8 mouse mode
            .replace(/\x1b\[7/g, '') // Save cursor
            .replace(/\x1b\[8/g, '') // Restore cursor
            .replace(/\]0;[^]*?\x07/g, '') // Set window title

            // Handle colors and formatting
            .replace(/\x1b\[0m/g, '</span>') // Reset
            .replace(/\x1b\[1m/g, '<span style="font-weight: bold;">') // Bold
            .replace(/\x1b\[2m/g, '<span style="opacity: 0.6;">') // Dim
            .replace(/\x1b\[3m/g, '<span style="font-style: italic;">') // Italic
            .replace(/\x1b\[4m/g, '<span style="text-decoration: underline;">') // Underline
            .replace(/\x1b\[7m/g, '<span style="background: #fff; color: #000;">') // Reverse

            // Standard colors (30-37)
            .replace(/\x1b\[30m/g, '<span style="color: #000;">') // Black
            .replace(/\x1b\[31m/g, '<span style="color: #ff6b6b;">') // Red
            .replace(/\x1b\[32m/g, '<span style="color: #51cf66;">') // Green
            .replace(/\x1b\[33m/g, '<span style="color: #ffd43b;">') // Yellow
            .replace(/\x1b\[34m/g, '<span style="color: #339af0;">') // Blue
            .replace(/\x1b\[35m/g, '<span style="color: #cc5de8;">') // Magenta
            .replace(/\x1b\[36m/g, '<span style="color: #22b8cf;">') // Cyan
            .replace(/\x1b\[37m/g, '<span style="color: #adb5bd;">') // White

            // Bright colors (90-97)
            .replace(/\x1b\[90m/g, '<span style="color: #6c757d;">') // Bright Black (Gray)
            .replace(/\x1b\[91m/g, '<span style="color: #ff8787;">') // Bright Red
            .replace(/\x1b\[92m/g, '<span style="color: #8ce99a;">') // Bright Green
            .replace(/\x1b\[93m/g, '<span style="color: #ffe066;">') // Bright Yellow
            .replace(/\x1b\[94m/g, '<span style="color: #74c0fc;">') // Bright Blue
            .replace(/\x1b\[95m/g, '<span style="color: #d0bfff;">') // Bright Magenta
            .replace(/\x1b\[96m/g, '<span style="color: #66d9ef;">') // Bright Cyan
            .replace(/\x1b\[97m/g, '<span style="color: #f8f9fa;">') // Bright White

            // Background colors (40-47)
            .replace(/\x1b\[40m/g, '<span style="background: #000;">') // Black bg
            .replace(/\x1b\[41m/g, '<span style="background: #ff6b6b;">') // Red bg
            .replace(/\x1b\[42m/g, '<span style="background: #51cf66;">') // Green bg
            .replace(/\x1b\[43m/g, '<span style="background: #ffd43b;">') // Yellow bg
            .replace(/\x1b\[44m/g, '<span style="background: #339af0;">') // Blue bg
            .replace(/\x1b\[45m/g, '<span style="background: #cc5de8;">') // Magenta bg
            .replace(/\x1b\[46m/g, '<span style="background: #22b8cf;">') // Cyan bg
            .replace(/\x1b\[47m/g, '<span style="background: #adb5bd;">') // White bg

            // Remove remaining escape sequences
            .replace(/\x1b\[[0-9;]*[a-zA-Z]/g, '')
            .replace(/\x1b\[[0-9;]*m/g, '')

            // Handle line endings
            .replace(/\r\n/g, '\n')
            .replace(/\r/g, '\n');

        return processed;
    }

    renderBlessedUI(data, outputLine) {
        const output = data.data;

        // Check if this is a blessed UI initialization
        if (output.includes('\x1b[?1049h') || output.includes('\x1b[?1h=')) {
            // Clear screen for blessed UI
            const consoleOutput = document.getElementById('console-output');
            consoleOutput.innerHTML = '';

            // Add blessed UI indicator
            const uiIndicator = document.createElement('div');
            uiIndicator.className = 'blessed-ui-indicator';
            uiIndicator.innerHTML = `
                <div class="ui-header">
                    <i class="fas fa-desktop"></i>
                    <span>Interactive Terminal UI Active</span>
                    <span class="ui-hint">Use arrow keys to navigate</span>
                </div>
            `;
            consoleOutput.appendChild(uiIndicator);

            // Enable blessed UI mode
            this.blessedUIMode = true;
            this.updateUIForBlessedMode(true);
        }

        // Check if blessed UI is exiting
        if (output.includes('\x1b[?1049l') || output.includes('\x1b[?1l>')) {
            this.blessedUIMode = false;
            this.updateUIForBlessedMode(false);
        }

        // Process blessed UI content
        if (this.blessedUIMode) {
            this.renderBlessedContent(output, outputLine);
        } else {
            // Regular processing
            outputLine.style.color = '#fff';
            const cleanedData = this.processAnsiCodes(output);
            outputLine.innerHTML = cleanedData;
        }
    }

    renderBlessedContent(output, outputLine) {
        // Handle blessed UI rendering
        outputLine.className += ' blessed-content';

        // Check for menu items or UI elements
        if (output.includes('Menu') || output.includes('┌') || output.includes('│')) {
            outputLine.className += ' blessed-menu';
            outputLine.style.color = '#fff';
            outputLine.style.backgroundColor = 'transparent';
        }

        // Check for selected items (highlighted in blessed)
        if (output.includes('selected') || output.includes('bg: "green"')) {
            outputLine.className += ' blessed-selected';
            outputLine.style.backgroundColor = '#28a745';
            outputLine.style.color = '#000';
        }

        // Process the content
        let processed = this.processAnsiCodes(output);

        // Handle blessed box drawing characters
        processed = processed
            .replace(/┌/g, '┌')
            .replace(/┐/g, '┐')
            .replace(/└/g, '└')
            .replace(/┘/g, '┘')
            .replace(/│/g, '│')
            .replace(/─/g, '─')
            .replace(/├/g, '├')
            .replace(/┤/g, '┤')
            .replace(/┬/g, '┬')
            .replace(/┴/g, '┴')
            .replace(/┼/g, '┼');

        outputLine.innerHTML = processed;
    }

    updateUIForBlessedMode(enabled) {
        const keyboardInfo = document.querySelector('.keyboard-info');
        const inputControls = document.querySelector('.input-controls');

        if (enabled) {
            keyboardInfo.innerHTML = `
                <span class="key-hint blessed-active">
                    <i class="fas fa-gamepad"></i>
                    Blessed UI Active - Use ↑↓ to navigate, Enter to select, Esc to exit
                </span>
            `;
            inputControls.style.display = 'flex';
        } else {
            keyboardInfo.innerHTML = `
                <span class="key-hint">Use keyboard: ↑↓←→ Enter Esc | Or click buttons below</span>
            `;
        }
    }

    closeModal() {
        document.getElementById('program-modal').style.display = 'none';
        this.currentProgram = null;
    }

    switchTab(tabName) {
        // Update tab buttons
        document.querySelectorAll('.tab-button').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');

        // Update tab content
        document.querySelectorAll('.tab-pane').forEach(pane => {
            pane.classList.remove('active');
        });
        document.getElementById(`${tabName}-tab`).classList.add('active');
    }

    async startProgram() {
        if (!this.currentProgram) return;

        try {
            const response = await fetch(`/api/programs/${this.currentProgram.id}/start`, {
                method: 'POST'
            });

            if (response.ok) {
                this.updateModalProgramStatus(true);
                this.updateProgramCardStatus(this.currentProgram.id, true);
                this.showSuccess('Program started successfully');
            } else {
                const error = await response.json();
                this.showError(error.error || 'Failed to start program');
            }
        } catch (error) {
            console.error('Error starting program:', error);
            this.showError('Failed to start program');
        }
    }

    async stopProgram() {
        if (!this.currentProgram) return;

        try {
            const response = await fetch(`/api/programs/${this.currentProgram.id}/stop`, {
                method: 'POST'
            });

            if (response.ok) {
                this.updateModalProgramStatus(false);
                this.updateProgramCardStatus(this.currentProgram.id, false);
                this.showSuccess('Program stopped successfully');
            } else {
                const error = await response.json();
                this.showError(error.error || 'Failed to stop program');
            }
        } catch (error) {
            console.error('Error stopping program:', error);
            this.showError('Failed to stop program');
        }
    }

    updateModalProgramStatus(isRunning) {
        const statusElement = document.getElementById('program-status');
        const startBtn = document.getElementById('start-program');
        const stopBtn = document.getElementById('stop-program');
        const inputField = document.getElementById('console-input-field');
        const sendBtn = document.getElementById('send-input');

        // Arrow key buttons
        const arrowBtns = [
            'arrow-up', 'arrow-down', 'arrow-left', 'arrow-right',
            'enter-key', 'escape-key', 'ctrl-c'
        ];

        statusElement.textContent = isRunning ? 'Running' : 'Stopped';
        statusElement.className = `status ${isRunning ? 'running' : 'stopped'}`;

        startBtn.disabled = isRunning;
        stopBtn.disabled = !isRunning;
        inputField.disabled = !isRunning;
        sendBtn.disabled = !isRunning;

        // Enable/disable arrow key buttons
        arrowBtns.forEach(btnId => {
            const btn = document.getElementById(btnId);
            if (btn) {
                btn.disabled = !isRunning;
            }
        });
    }

    handleKeyboardInput(e) {
        // Only handle if console tab is active and program is running
        const consoleTab = document.getElementById('console-tab');
        if (!consoleTab.classList.contains('active')) return;

        const statusElement = document.getElementById('program-status');
        if (!statusElement || statusElement.textContent !== 'Running') return;

        // Prevent default for navigation keys
        if (['ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight', 'Enter', 'Escape'].includes(e.key)) {
            e.preventDefault();
            this.sendSpecialKey(e.key);
        } else if (e.ctrlKey && e.key === 'c') {
            e.preventDefault();
            this.sendSpecialKey('CtrlC');
        }
    }

    sendSpecialKey(keyName) {
        if (!this.currentProgram) return;

        let keyCode = '';
        let displayName = keyName;

        switch(keyName) {
            case 'ArrowUp':
                keyCode = '\x1b[A';
                displayName = '↑';
                break;
            case 'ArrowDown':
                keyCode = '\x1b[B';
                displayName = '↓';
                break;
            case 'ArrowRight':
                keyCode = '\x1b[C';
                displayName = '→';
                break;
            case 'ArrowLeft':
                keyCode = '\x1b[D';
                displayName = '←';
                break;
            case 'Enter':
                keyCode = '\r';
                displayName = '⏎';
                break;
            case 'Escape':
                keyCode = '\x1b';
                displayName = 'ESC';
                break;
            case 'CtrlC':
                keyCode = '\x03';
                displayName = 'Ctrl+C';
                break;
            default:
                return;
        }

        // Send special key to server
        fetch(`/api/programs/${this.currentProgram.id}/input`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ input: keyCode })
        }).then(response => {
            if (response.ok) {
                // Show key press in console with visual feedback
                this.appendConsoleOutput({
                    programId: this.currentProgram.id,
                    type: 'input',
                    data: `[${displayName}]`
                });

                // Visual feedback for key press
                this.showKeyPressEffect(keyName);
            } else {
                this.showError(`Failed to send ${keyName} key`);
            }
        }).catch(error => {
            console.error('Error sending special key:', error);
            this.showError(`Failed to send ${keyName} key`);
        });
    }

    showKeyPressEffect(keyName) {
        // Visual feedback for button press
        const buttonMap = {
            'ArrowUp': 'arrow-up',
            'ArrowDown': 'arrow-down',
            'ArrowLeft': 'arrow-left',
            'ArrowRight': 'arrow-right',
            'Enter': 'enter-key',
            'Escape': 'escape-key',
            'CtrlC': 'ctrl-c'
        };

        const buttonId = buttonMap[keyName];
        if (buttonId) {
            const button = document.getElementById(buttonId);
            if (button) {
                button.style.transform = 'scale(0.95)';
                button.style.boxShadow = '0 2px 8px rgba(102, 126, 234, 0.4)';
                setTimeout(() => {
                    button.style.transform = '';
                    button.style.boxShadow = '';
                }, 150);
            }
        }
    }

    sendInput() {
        const inputField = document.getElementById('console-input-field');
        const input = inputField.value.trim();

        if (!input || !this.currentProgram) return;

        // Send input to server
        fetch(`/api/programs/${this.currentProgram.id}/input`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ input: input + '\n' })
        }).then(response => {
            if (response.ok) {
                // Show input in console
                this.appendConsoleOutput({
                    programId: this.currentProgram.id,
                    type: 'input',
                    data: `> ${input}\n`
                });
                inputField.value = '';
            } else {
                this.showError('Failed to send input to program');
            }
        }).catch(error => {
            console.error('Error sending input:', error);
            this.showError('Failed to send input to program');
        });
    }

    clearConsole() {
        document.getElementById('console-output').innerHTML = '';
    }

    appendConsoleOutput(data) {
        // Store output for all programs, not just current one
        if (!this.programOutputs) {
            this.programOutputs = new Map();
        }

        if (!this.programOutputs.has(data.programId)) {
            this.programOutputs.set(data.programId, []);
        }

        // Store the output data
        this.programOutputs.get(data.programId).push(data);

        // Only display if this is the currently viewed program
        if (!this.currentProgram || data.programId !== this.currentProgram.id) return;

        const consoleOutput = document.getElementById('console-output');
        const outputLine = document.createElement('div');
        outputLine.className = `output-line ${data.type}`;
        outputLine.setAttribute('data-timestamp', new Date().toISOString());

        // Set colors based on output type
        switch(data.type) {
            case 'info':
                outputLine.style.color = '#61dafb';
                break;
            case 'success':
                outputLine.style.color = '#28a745';
                break;
            case 'error':
                outputLine.style.color = '#dc3545';
                break;
            case 'stderr':
                outputLine.style.color = '#ffc107';
                break;
            case 'install':
            case 'install-error':
                outputLine.style.color = '#17a2b8';
                break;
            case 'build':
            case 'build-error':
                outputLine.style.color = '#6f42c1';
                break;
            case 'input':
                outputLine.style.color = '#20c997';
                outputLine.style.fontWeight = 'bold';
                break;
            case 'blessed-ui':
                // Special handling for blessed UI
                this.renderBlessedUI(data, outputLine);
                break;
            case 'menu-prompt':
                outputLine.style.color = '#ffd43b';
                outputLine.style.fontWeight = 'bold';
                this.showMenuPrompt(data);
                break;
            case 'input-prompt':
                outputLine.style.color = '#22b8cf';
                outputLine.style.fontWeight = 'bold';
                this.showInputPrompt(data);
                break;
            case 'wallet-info':
                outputLine.style.color = '#51cf66';
                break;
            case 'transaction-log':
                outputLine.style.color = '#339af0';
                break;
            default:
                outputLine.style.color = '#fff';
        }

        // Handle ANSI escape codes for better CLI display
        const cleanedData = this.processAnsiCodes(data.data);
        outputLine.innerHTML = cleanedData;

        consoleOutput.appendChild(outputLine);
        consoleOutput.scrollTop = consoleOutput.scrollHeight;

        // Limit console output to prevent memory issues
        const maxLines = 1000;
        while (consoleOutput.children.length > maxLines) {
            consoleOutput.removeChild(consoleOutput.firstChild);
        }
    }

    async loadProgramFiles() {
        if (!this.currentProgram) return;

        try {
            const response = await fetch(`/api/programs/${this.currentProgram.id}/files`);
            const files = await response.json();

            this.renderFilesList(files);
            this.populateFileSelector(files);
        } catch (error) {
            console.error('Error loading files:', error);
            this.showError('Failed to load program files');
        }
    }

    renderFilesList(files) {
        const filesList = document.getElementById('files-list');
        filesList.innerHTML = '';

        files.forEach(file => {
            const fileItem = document.createElement('div');
            fileItem.className = 'file-item';
            fileItem.innerHTML = `
                <div class="file-info">
                    <i class="fas fa-file-code file-icon"></i>
                    <span>${file.name}</span>
                </div>
                <div class="file-meta">
                    <small>${this.formatFileSize(file.size)} • ${this.formatDate(file.modified)}</small>
                </div>
            `;

            fileItem.addEventListener('click', () => {
                document.getElementById('file-selector').value = file.name;
                this.loadFileContent(file.name);
                this.switchTab('config');
            });

            filesList.appendChild(fileItem);
        });
    }

    populateFileSelector(files) {
        const selector = document.getElementById('file-selector');
        selector.innerHTML = '<option value="">Select a file to edit</option>';

        files.forEach(file => {
            const option = document.createElement('option');
            option.value = file.name;
            option.textContent = file.name;
            selector.appendChild(option);
        });
    }

    async loadFileContent(filename) {
        if (!filename || !this.currentProgram) return;

        try {
            const response = await fetch(`/api/programs/${this.currentProgram.id}/files/${filename}`);
            const data = await response.json();

            document.getElementById('file-editor').value = data.content;
        } catch (error) {
            console.error('Error loading file content:', error);
            this.showError('Failed to load file content');
        }
    }

    async saveFile() {
        const filename = document.getElementById('file-selector').value;
        const content = document.getElementById('file-editor').value;

        if (!filename || !this.currentProgram) {
            this.showError('Please select a file to save');
            return;
        }

        try {
            const response = await fetch(`/api/programs/${this.currentProgram.id}/files/${filename}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ content })
            });

            if (response.ok) {
                this.showSuccess('File saved successfully');
            } else {
                const error = await response.json();
                this.showError(error.error || 'Failed to save file');
            }
        } catch (error) {
            console.error('Error saving file:', error);
            this.showError('Failed to save file');
        }
    }

    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    formatDate(dateString) {
        return new Date(dateString).toLocaleDateString();
    }

    showLoading(show) {
        document.getElementById('loading-overlay').style.display = show ? 'flex' : 'none';
    }

    showSuccess(message) {
        this.showNotification(message, 'success');
    }

    showError(message) {
        this.showNotification(message, 'error');
    }

    showNotification(message, type) {
        // Simple notification - you could enhance this with a proper notification library
        const notification = document.createElement('div');
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 8px;
            color: white;
            font-weight: 500;
            z-index: 10000;
            background: ${type === 'success' ? '#28a745' : '#dc3545'};
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        `;
        notification.textContent = message;

        document.body.appendChild(notification);

        setTimeout(() => {
            notification.remove();
        }, 3000);
    }

    updateProgramStatus(data) {
        this.updateProgramCardStatus(data.programId, data.status === 'running');

        if (this.currentProgram && this.currentProgram.id === data.programId) {
            this.updateModalProgramStatus(data.status === 'running');
        }
    }

    toggleTemplateOptions() {
        const templateOptions = document.getElementById('template-options');
        const isVisible = templateOptions.style.display !== 'none';
        templateOptions.style.display = isVisible ? 'none' : 'block';
    }

    async createTemplate(templateName) {
        if (!this.currentProgram) return;

        try {
            const response = await fetch(`/api/programs/${this.currentProgram.id}/create-template/${templateName}`, {
                method: 'POST'
            });

            if (response.ok) {
                const data = await response.json();

                // Update file selector and load the new file
                await this.loadProgramFiles();
                document.getElementById('file-selector').value = templateName;
                document.getElementById('file-editor').value = data.content;

                // Hide template options
                document.getElementById('template-options').style.display = 'none';

                this.showSuccess(`Template file ${templateName} created successfully!`);
            } else {
                const error = await response.json();
                this.showError(error.error || 'Failed to create template file');
            }
        } catch (error) {
            console.error('Error creating template:', error);
            this.showError('Failed to create template file');
        }
    }

    showMenuPrompt(data) {
        // Enable number input for menu selection
        const inputField = document.getElementById('console-input-field');
        const keyboardInfo = document.querySelector('.keyboard-info');

        if (inputField && keyboardInfo) {
            inputField.placeholder = 'Enter menu number (e.g., 1, 2, 3...)';
            keyboardInfo.innerHTML = `
                <span class="key-hint menu-active">
                    <i class="fas fa-list"></i>
                    Menu Active - Type number and press Enter
                </span>
            `;
        }
    }

    showInputPrompt(data) {
        // Enable text input for prompts
        const inputField = document.getElementById('console-input-field');
        const keyboardInfo = document.querySelector('.keyboard-info');

        if (inputField && keyboardInfo) {
            inputField.placeholder = 'Type your response and press Enter';
            keyboardInfo.innerHTML = `
                <span class="key-hint prompt-active">
                    <i class="fas fa-keyboard"></i>
                    Input Required - Type response and press Enter
                </span>
            `;
        }
    }

    createTemplate(templateName) {
        if (!this.currentProgram) return;

        try {
            fetch(`/api/programs/${this.currentProgram.id}/create-template/${templateName}`, {
                method: 'POST'
            }).then(response => {
                if (response.ok) {
                    return response.json();
                } else {
                    throw new Error('Failed to create template');
                }
            }).then(data => {
                // Update file selector and load the new file
                this.loadProgramFiles().then(() => {
                    document.getElementById('file-selector').value = templateName;
                    document.getElementById('file-editor').value = data.content;

                    // Hide template options
                    document.getElementById('template-options').style.display = 'none';

                    this.showSuccess(`Template file ${templateName} created successfully!`);
                });
            }).catch(error => {
                console.error('Error creating template:', error);
                this.showError('Failed to create template file');
            });
        } catch (error) {
            console.error('Error creating template:', error);
            this.showError('Failed to create template file');
        }
    }
}

// Initialize dashboard when page loads
const dashboard = new TestnetDashboard();
