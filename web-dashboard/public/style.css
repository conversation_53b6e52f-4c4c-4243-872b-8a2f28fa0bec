* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
}

.header {
    text-align: center;
    margin-bottom: 30px;
    color: white;
}

.header h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.header p {
    font-size: 1.1rem;
    opacity: 0.9;
}

.stats-bar {
    display: flex;
    justify-content: center;
    gap: 30px;
    margin-bottom: 30px;
    flex-wrap: wrap;
}

.stat-item {
    background: rgba(255,255,255,0.1);
    padding: 15px 25px;
    border-radius: 10px;
    color: white;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255,255,255,0.2);
}

.stat-item i {
    margin-right: 8px;
}

.filter-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    gap: 20px;
    flex-wrap: wrap;
}

.filter-group, .search-group {
    display: flex;
    align-items: center;
    gap: 10px;
    color: white;
}

.filter-group select, .search-group input {
    padding: 10px 15px;
    border: none;
    border-radius: 8px;
    background: rgba(255,255,255,0.9);
    font-size: 14px;
    min-width: 200px;
}

.search-group {
    position: relative;
}

.search-group i {
    position: absolute;
    left: 15px;
    color: #666;
}

.search-group input {
    padding-left: 40px;
}

.programs-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.program-card {
    background: white;
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    cursor: pointer;
    border: 2px solid transparent;
}

.program-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0,0,0,0.15);
    border-color: #667eea;
}

.program-card.running {
    border-color: #28a745;
    background: linear-gradient(135deg, #f8fff9 0%, #e8f5e8 100%);
}

.program-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 15px;
}

.program-title {
    font-size: 1.3rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 5px;
}

.program-type {
    background: #667eea;
    color: white;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    text-transform: uppercase;
    font-weight: 500;
}

.program-description {
    color: #666;
    line-height: 1.5;
    margin-bottom: 20px;
}

.program-actions {
    display: flex;
    gap: 10px;
}

.btn {
    padding: 10px 20px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

.btn-primary {
    background: #667eea;
    color: white;
}

.btn-success {
    background: #28a745;
    color: white;
}

.btn-danger {
    background: #dc3545;
    color: white;
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.status {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
    text-transform: uppercase;
}

.status.running {
    background: #d4edda;
    color: #155724;
}

.status.stopped {
    background: #f8d7da;
    color: #721c24;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
    backdrop-filter: blur(5px);
}

.modal-content {
    background-color: white;
    margin: 2% auto;
    padding: 0;
    border-radius: 15px;
    width: 90%;
    max-width: 1200px;
    height: 90vh;
    display: flex;
    flex-direction: column;
    box-shadow: 0 20px 60px rgba(0,0,0,0.3);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 30px;
    border-bottom: 1px solid #eee;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px 15px 0 0;
}

.modal-header h2 {
    margin: 0;
    font-size: 1.5rem;
}

.close {
    color: white;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    transition: opacity 0.3s ease;
}

.close:hover {
    opacity: 0.7;
}

.modal-body {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.tabs {
    display: flex;
    border-bottom: 1px solid #eee;
    background: #f8f9fa;
}

.tab-button {
    padding: 15px 25px;
    border: none;
    background: none;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    color: #666;
    transition: all 0.3s ease;
}

.tab-button.active {
    background: white;
    color: #667eea;
    border-bottom: 3px solid #667eea;
}

.tab-content {
    flex: 1;
    overflow: hidden;
}

.tab-pane {
    display: none;
    height: 100%;
    padding: 20px;
    overflow-y: auto;
}

.tab-pane.active {
    display: flex;
    flex-direction: column;
}

.console-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 15px;
    border-bottom: 1px solid #eee;
}

.console-controls {
    display: flex;
    gap: 10px;
}

.terminal-container {
    position: relative;
    flex: 1;
    background: #000;
    border: 2px solid #333;
    border-radius: 4px;
    overflow: hidden;
    min-height: 500px;
    max-height: 600px;
}

.console-output {
    background: #000;
    color: #fff;
    padding: 10px;
    font-family: 'Courier New', 'Monaco', 'Menlo', monospace;
    font-size: 13px;
    line-height: 1.2;
    overflow-y: auto;
    white-space: pre;
    word-wrap: break-word;
    height: 100%;
    position: relative;
}

.terminal-screen {
    /* Terminal-specific styling */
    background: #000;
    color: #fff;
    border: none;
    border-radius: 0;
}

.terminal-cursor {
    position: absolute;
    width: 8px;
    height: 16px;
    background: #fff;
    animation: blink 1s infinite;
    pointer-events: none;
    z-index: 10;
}

@keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0; }
}

.console-output .output-line {
    margin-bottom: 2px;
}

.console-output .output-line.info {
    color: #61dafb;
}

.console-output .output-line.success {
    color: #28a745;
}

.console-output .output-line.error {
    color: #dc3545;
}

.console-output .output-line.stderr {
    color: #ffc107;
}

.console-output .output-line.install {
    color: #17a2b8;
}

.console-output .output-line.build {
    color: #6f42c1;
}

.console-output .output-line.input {
    color: #20c997;
    font-weight: bold;
}

.console-input {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px solid #333;
}

.keyboard-info {
    text-align: center;
    padding: 5px;
    background: rgba(102, 126, 234, 0.1);
    border-radius: 4px;
    border: 1px solid rgba(102, 126, 234, 0.3);
}

.key-hint {
    font-size: 12px;
    color: #667eea;
    font-weight: 500;
}

.input-controls {
    display: flex;
    gap: 5px;
    justify-content: center;
    flex-wrap: wrap;
}

.arrow-btn {
    padding: 8px 12px;
    font-size: 12px;
    min-width: 40px;
    border-radius: 4px;
}

.arrow-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.console-input .input-row {
    display: flex;
    gap: 10px;
}

.console-input input {
    flex: 1;
    padding: 10px 15px;
    border: 1px solid #444;
    border-radius: 5px;
    background: #2d2d2d;
    color: #fff;
    font-family: 'Courier New', monospace;
    font-size: 14px;
}

.console-input input:focus {
    outline: none;
    border-color: #667eea;
}

.console-input input:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.files-header, .config-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #eee;
}

.files-list {
    display: grid;
    gap: 10px;
}

.file-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    cursor: pointer;
    transition: background 0.3s ease;
}

.file-item:hover {
    background: #e9ecef;
}

.file-info {
    display: flex;
    align-items: center;
    gap: 10px;
}

.file-icon {
    color: #667eea;
}

.config-controls {
    display: flex;
    gap: 15px;
    align-items: center;
}

.config-controls select {
    padding: 8px 15px;
    border: 1px solid #ddd;
    border-radius: 5px;
    min-width: 200px;
}

.file-editor {
    flex: 1;
    width: 100%;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 15px;
    font-family: 'Courier New', monospace;
    font-size: 14px;
    line-height: 1.4;
    resize: none;
    outline: none;
}

.template-options {
    margin: 15px 0;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #dee2e6;
}

.template-options h4 {
    margin: 0 0 10px 0;
    color: #495057;
    font-size: 14px;
}

.template-buttons {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.template-btn {
    padding: 8px 15px;
    font-size: 12px;
    border-radius: 5px;
}

.loading-overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255,255,255,0.9);
    z-index: 9999;
    justify-content: center;
    align-items: center;
}

.loading-spinner {
    text-align: center;
    color: #667eea;
}

.loading-spinner i {
    font-size: 3rem;
    margin-bottom: 15px;
}

.loading-spinner p {
    font-size: 1.1rem;
    font-weight: 500;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 15px;
    }

    .header h1 {
        font-size: 2rem;
    }

    .stats-bar {
        gap: 15px;
    }

    .stat-item {
        padding: 10px 15px;
        font-size: 0.9rem;
    }

    .filter-bar {
        flex-direction: column;
        align-items: stretch;
    }

    .programs-grid {
        grid-template-columns: 1fr;
    }

    .modal-content {
        width: 95%;
        height: 95vh;
        margin: 2.5% auto;
    }

    .modal-header {
        padding: 15px 20px;
    }

    .tab-pane {
        padding: 15px;
    }
}
