<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Testnet Program Dashboard</title>
    <link rel="stylesheet" href="style.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container">
        <header class="header">
            <h1><i class="fas fa-rocket"></i> Testnet Program Dashboard</h1>
            <p>Manage and run all your testnet programs from one place</p>
        </header>

        <div class="stats-bar">
            <div class="stat-item">
                <i class="fas fa-list"></i>
                <span>Total Programs: <span id="total-programs">0</span></span>
            </div>
            <div class="stat-item">
                <i class="fas fa-play-circle"></i>
                <span>Running: <span id="running-programs">0</span></span>
            </div>
            <div class="stat-item">
                <i class="fas fa-stop-circle"></i>
                <span>Stopped: <span id="stopped-programs">0</span></span>
            </div>
        </div>

        <div class="filter-bar">
            <div class="filter-group">
                <label for="type-filter">Filter by Type:</label>
                <select id="type-filter">
                    <option value="">All Types</option>
                    <option value="blockchain">Blockchain</option>
                    <option value="defi">DeFi</option>
                    <option value="bot">Bot</option>
                    <option value="protocol">Protocol</option>
                    <option value="deployment">Deployment</option>
                    <option value="trading">Trading</option>
                    <option value="storage">Storage</option>
                    <option value="nft">NFT</option>
                    <option value="testnet">Testnet</option>
                    <option value="referral">Referral</option>
                    <option value="testing">Testing</option>
                </select>
            </div>
            <div class="search-group">
                <i class="fas fa-search"></i>
                <input type="text" id="search-input" placeholder="Search programs...">
            </div>
        </div>

        <div class="programs-grid" id="programs-grid">
            <!-- Programs will be loaded here -->
        </div>

        <!-- Program Detail Modal -->
        <div id="program-modal" class="modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h2 id="modal-title">Program Details</h2>
                    <span class="close" id="modal-close">&times;</span>
                </div>
                <div class="modal-body">
                    <div class="tabs">
                        <button class="tab-button active" data-tab="console">Console</button>
                        <button class="tab-button" data-tab="files">Files</button>
                        <button class="tab-button" data-tab="config">Config</button>
                    </div>

                    <div class="tab-content">
                        <div id="console-tab" class="tab-pane active">
                            <div class="console-header">
                                <div class="console-controls">
                                    <button id="start-program" class="btn btn-success">
                                        <i class="fas fa-play"></i> Start
                                    </button>
                                    <button id="stop-program" class="btn btn-danger">
                                        <i class="fas fa-stop"></i> Stop
                                    </button>
                                    <button id="clear-console" class="btn btn-secondary">
                                        <i class="fas fa-trash"></i> Clear
                                    </button>
                                </div>
                                <div class="status-indicator">
                                    <span id="program-status" class="status stopped">Stopped</span>
                                </div>
                            </div>
                            <div id="terminal-container" class="terminal-container">
                                <div id="console-output" class="console-output terminal-screen"></div>
                                <div class="terminal-cursor" id="terminal-cursor"></div>
                            </div>
                            <div class="console-input">
                                <div class="keyboard-info">
                                    <span class="key-hint">Use keyboard: ↑↓←→ Enter Esc | Or click buttons below</span>
                                </div>
                                <div class="input-controls">
                                    <button id="arrow-up" class="btn btn-secondary arrow-btn" disabled title="Arrow Up (↑)">
                                        <i class="fas fa-arrow-up"></i>
                                    </button>
                                    <button id="arrow-down" class="btn btn-secondary arrow-btn" disabled title="Arrow Down (↓)">
                                        <i class="fas fa-arrow-down"></i>
                                    </button>
                                    <button id="arrow-left" class="btn btn-secondary arrow-btn" disabled title="Arrow Left (←)">
                                        <i class="fas fa-arrow-left"></i>
                                    </button>
                                    <button id="arrow-right" class="btn btn-secondary arrow-btn" disabled title="Arrow Right (→)">
                                        <i class="fas fa-arrow-right"></i>
                                    </button>
                                    <button id="enter-key" class="btn btn-success arrow-btn" disabled title="Enter">
                                        <i class="fas fa-level-down-alt"></i>
                                    </button>
                                    <button id="escape-key" class="btn btn-warning arrow-btn" disabled title="Escape">
                                        <i class="fas fa-times"></i>
                                    </button>
                                    <button id="ctrl-c" class="btn btn-danger arrow-btn" disabled title="Ctrl+C">
                                        <i class="fas fa-stop"></i>
                                    </button>
                                </div>
                                <div class="input-row">
                                    <input type="text" id="console-input-field" placeholder="Type text input or use keyboard navigation..." disabled>
                                    <button id="send-input" class="btn btn-primary" disabled>
                                        <i class="fas fa-paper-plane"></i> Send
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div id="files-tab" class="tab-pane">
                            <div class="files-header">
                                <h3>Program Files</h3>
                                <button id="refresh-files" class="btn btn-primary">
                                    <i class="fas fa-sync"></i> Refresh
                                </button>
                            </div>
                            <div id="files-list" class="files-list"></div>
                        </div>

                        <div id="config-tab" class="tab-pane">
                            <div class="config-header">
                                <h3>Edit File</h3>
                                <div class="config-controls">
                                    <select id="file-selector">
                                        <option value="">Select a file to edit</option>
                                    </select>
                                    <button id="save-file" class="btn btn-success">
                                        <i class="fas fa-save"></i> Save
                                    </button>
                                    <button id="create-template" class="btn btn-primary">
                                        <i class="fas fa-plus"></i> Create Template
                                    </button>
                                </div>
                            </div>
                            <div id="template-options" class="template-options" style="display: none;">
                                <h4>Create Template File:</h4>
                                <div class="template-buttons">
                                    <button class="btn btn-secondary template-btn" data-template="pvkey.txt">pvkey.txt</button>
                                    <button class="btn btn-secondary template-btn" data-template="accounts.txt">accounts.txt</button>
                                    <button class="btn btn-secondary template-btn" data-template=".env">.env</button>
                                    <button class="btn btn-secondary template-btn" data-template="wallet.json">wallet.json</button>
                                </div>
                            </div>
                            <textarea id="file-editor" class="file-editor" placeholder="Select a file to edit..."></textarea>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Loading overlay -->
        <div id="loading-overlay" class="loading-overlay">
            <div class="loading-spinner">
                <i class="fas fa-spinner fa-spin"></i>
                <p>Loading...</p>
            </div>
        </div>
    </div>

    <script src="/socket.io/socket.io.js"></script>
    <script src="app.js"></script>
</body>
</html>
