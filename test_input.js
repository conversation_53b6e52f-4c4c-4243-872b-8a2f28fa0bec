#!/usr/bin/env node

// Test script untuk memverifikasi input handling
const http = require('http');

function makeRequest(method, path, data = null) {
    return new Promise((resolve, reject) => {
        const options = {
            hostname: 'localhost',
            port: 3000,
            path: path,
            method: method,
            headers: {
                'Content-Type': 'application/json'
            }
        };

        const req = http.request(options, (res) => {
            let body = '';
            res.on('data', (chunk) => {
                body += chunk;
            });
            res.on('end', () => {
                try {
                    const response = JSON.parse(body);
                    resolve({ status: res.statusCode, data: response });
                } catch (e) {
                    resolve({ status: res.statusCode, data: body });
                }
            });
        });

        req.on('error', (err) => {
            reject(err);
        });

        if (data) {
            req.write(JSON.stringify(data));
        }
        req.end();
    });
}

async function testInputHandling() {
    console.log('🧪 Testing Input Handling for 0G Network...\n');

    try {
        // 1. Start 0G Network program
        console.log('1. Starting 0G Network program...');
        const startResult = await makeRequest('POST', '/api/programs/0g/start');
        console.log(`   Status: ${startResult.status}`);
        console.log(`   Response: ${JSON.stringify(startResult.data)}\n`);

        if (startResult.status !== 200) {
            console.log('❌ Failed to start program');
            return;
        }

        // Wait for program to initialize
        console.log('2. Waiting for program to initialize...');
        await new Promise(resolve => setTimeout(resolve, 5000));

        // 2. Send input "5"
        console.log('3. Sending input "5"...');
        const inputResult = await makeRequest('POST', '/api/programs/0g/input', { input: '5' });
        console.log(`   Status: ${inputResult.status}`);
        console.log(`   Response: ${JSON.stringify(inputResult.data)}\n`);

        // Wait for processing
        await new Promise(resolve => setTimeout(resolve, 3000));

        // 3. Send input "10"
        console.log('4. Sending input "10"...');
        const inputResult2 = await makeRequest('POST', '/api/programs/0g/input', { input: '10' });
        console.log(`   Status: ${inputResult2.status}`);
        console.log(`   Response: ${JSON.stringify(inputResult2.data)}\n`);

        // Wait for processing
        await new Promise(resolve => setTimeout(resolve, 3000));

        // 4. Stop program
        console.log('5. Stopping program...');
        const stopResult = await makeRequest('POST', '/api/programs/0g/stop');
        console.log(`   Status: ${stopResult.status}`);
        console.log(`   Response: ${JSON.stringify(stopResult.data)}\n`);

        console.log('✅ Test completed successfully!');

    } catch (error) {
        console.error('❌ Test failed:', error.message);
    }
}

// Test different input types
async function testDifferentInputs() {
    console.log('🧪 Testing Different Input Types...\n');

    const testInputs = [
        { value: '1', description: 'Single digit' },
        { value: '10', description: 'Double digit' },
        { value: '100', description: 'Triple digit' },
        { value: 'test', description: 'Text input' },
        { value: '5.5', description: 'Decimal number' },
        { value: '', description: 'Empty input' }
    ];

    for (const test of testInputs) {
        console.log(`Testing ${test.description}: "${test.value}"`);

        try {
            const result = await makeRequest('POST', '/api/programs/0g/input', { input: test.value });
            console.log(`   Status: ${result.status}`);
            console.log(`   Response: ${JSON.stringify(result.data)}`);
        } catch (error) {
            console.log(`   Error: ${error.message}`);
        }

        console.log('');
        await new Promise(resolve => setTimeout(resolve, 1000));
    }
}

// Main execution
async function main() {
    console.log('🚀 Input Handling Test Suite\n');
    console.log('='.repeat(50));

    // Test basic input handling
    await testInputHandling();

    console.log('\n' + '='.repeat(50));

    // Test different input types (only if program is running)
    // await testDifferentInputs();

    console.log('\n🎉 All tests completed!');
}

if (require.main === module) {
    main().catch(console.error);
}

module.exports = { makeRequest, testInputHandling, testDifferentInputs };
