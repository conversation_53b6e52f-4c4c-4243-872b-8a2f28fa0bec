# Testnet Program Terminal Emulator

Terminal emulator yang dirancang khusus untuk menangani berbagai jenis program testnet dengan pola interaksi yang berbeda-beda. Berdasarkan analisis mendalam terhadap 25+ program testnet yang berbeda.

## 🚀 Fitur Utama

- **Auto-Detection**: Mendeteksi jenis program berdasarkan output pattern
- **Multi-Input Support**: Mendukung berbagai jenis input (arrow keys, text, angka)
- **Blessed UI Handling**: <PERSON><PERSON> blessed terminal UI dengan navigasi arrow key
- **ANSI Code Processing**: Konversi ANSI escape codes ke HTML
- **Real-time Interaction**: Interaksi real-time dengan program yang berjalan
- **Web Dashboard Integration**: Otomatis buka browser untuk web dashboard
- **Error Handling**: Penanganan error yang jelas dan informatif

## 📋 Jenis Program yang Didukung

### 1. Blessed UI Programs
Program yang menggunakan blessed library untuk interface terminal interaktif.

**Contoh**: Maitrix, T1 Bridge, Huddle Protocol

**Interaksi**:
- ↑↓←→ untuk navigasi menu
- Enter untuk pilih menu
- Escape untuk kembali
- Ctrl+C untuk keluar

### 2. Console Menu Programs
Program dengan menu berbasis teks dan input angka.

**Contoh**: Rise Protocol, Pharos Protocol

**Interaksi**:
- Ketik angka pilihan + Enter

### 3. Prompt Input Programs
Program yang meminta input spesifik dari user.

**Contoh**: TradeGPT, 0G Network, INFTS Protocol

**Interaksi**:
- Ketik text/angka + Enter

### 4. Auto-Run Programs
Program yang berjalan otomatis tanpa interaksi user.

**Contoh**: Enso Finance, Rome EVM Deployer

**Interaksi**: Tidak ada (auto-run)

### 5. Web Dashboard Programs
Program yang menyediakan interface web dashboard.

**Contoh**: Union, Monad Testnet

**Interaksi**: Akses melalui browser

### 6. Loading/Error Programs
Program dengan state loading atau error.

**Contoh**: Kite AI, Shift Protocol

**Interaksi**: Menunggu atau handle error

## 🛠️ Implementasi

### File Utama

1. **`demo.txt`** - Analisis lengkap semua program testnet
2. **`terminal-emulator-implementation.js`** - Core terminal emulator
3. **`terminal-demo.html`** - Demo interaktif
4. **`TERMINAL_EMULATOR_README.md`** - Dokumentasi

### Struktur Kelas TerminalEmulator

```javascript
class TerminalEmulator {
  constructor(containerId)
  
  // Core Methods
  detectProgramType(output)     // Deteksi jenis program
  processOutput(data)           // Proses output program
  handleKeyDown(e)              // Handle keyboard input
  
  // Program Type Handlers
  initBlessedUI()               // Setup blessed UI
  initConsoleMenu()             // Setup console menu
  initPromptInput()             // Setup prompt input
  handleWebDashboard()          // Handle web dashboard
  
  // Utility Methods
  cleanBlessedOutput(output)    // Clean ANSI codes
  ansiToHtml(text)              // Convert ANSI to HTML
  sendInput(input)              // Send input ke program
}
```

### Pattern Detection

Terminal emulator menggunakan pattern detection untuk mengenali jenis program:

```javascript
// Blessed UI detection
if (output.includes('┌') || output.includes('│') || output.includes('█')) {
  return 'blessed-ui';
}

// Console menu detection
if (output.includes('===== MAIN MENU =====') || /\d+\.\s/.test(output)) {
  return 'console-menu';
}

// Prompt input detection
if (output.includes('Enter the number') || output.trim().endsWith(': ')) {
  return 'prompt-input';
}
```

## 🎮 Cara Penggunaan

### 1. Buka Demo
```bash
# Buka file terminal-demo.html di browser
open terminal-demo.html
```

### 2. Pilih Program
Klik salah satu tombol program untuk memulai simulasi:
- **Maitrix** - Blessed UI dengan navigasi arrow key
- **Rise Protocol** - Console menu dengan input angka
- **TradeGPT** - Prompt input dengan validasi
- **Enso Finance** - Auto-run tanpa interaksi

### 3. Interaksi
Setiap program memiliki cara interaksi yang berbeda:
- **Blessed UI**: Gunakan arrow keys
- **Console Menu**: Ketik angka + Enter
- **Prompt Input**: Ketik text + Enter
- **Web Dashboard**: Klik tombol "Open Dashboard"

## 🔧 Integrasi dengan Program Nyata

Untuk mengintegrasikan dengan program testnet yang sebenarnya:

```javascript
// 1. Inisialisasi terminal
const terminal = new TerminalEmulator('terminal-container');

// 2. Start program
terminal.startProgram('program-name');

// 3. Listen untuk input
document.addEventListener('terminalInput', (e) => {
  // Kirim input ke program yang berjalan
  programProcess.stdin.write(e.detail.input);
});

// 4. Process output dari program
programProcess.stdout.on('data', (data) => {
  terminal.processOutput(data.toString());
});
```

## 📊 Analisis Program

Berdasarkan analisis 25+ program testnet, ditemukan pola-pola berikut:

### Distribution by Type:
- **Blessed UI**: 12% (3 programs)
- **Console Menu**: 20% (5 programs)
- **Prompt Input**: 32% (8 programs)
- **Auto-Run**: 28% (7 programs)
- **Web Dashboard**: 8% (2 programs)

### Common Patterns:
- **ASCII Art Headers**: 80% program menggunakan ASCII art
- **Progress Indicators**: 60% program menampilkan progress
- **Error Handling**: 90% program memiliki error handling
- **Proxy Support**: 70% program mendukung proxy

## 🎯 Fitur Lanjutan

### 1. ANSI Code Support
- Color codes (red, green, yellow, blue, etc.)
- Text formatting (bold, italic, underline)
- Cursor positioning
- Screen clearing

### 2. Keyboard Handling
- Arrow keys untuk blessed UI
- Ctrl+C untuk interrupt
- Tab completion (future)
- Command history

### 3. Visual Enhancements
- Syntax highlighting
- Progress bars
- Status indicators
- Theme support

## 🔮 Roadmap

### Phase 1 (Current)
- [x] Basic terminal emulator
- [x] Program type detection
- [x] Blessed UI support
- [x] Demo implementation

### Phase 2 (Next)
- [ ] Real program integration
- [ ] WebSocket communication
- [ ] File upload/download
- [ ] Session persistence

### Phase 3 (Future)
- [ ] Multi-tab support
- [ ] Plugin system
- [ ] Advanced theming
- [ ] Performance optimization

## 🤝 Contributing

1. Fork repository
2. Buat feature branch
3. Commit changes
4. Push ke branch
5. Create Pull Request

## 📝 License

MIT License - lihat file LICENSE untuk detail.

## 🙏 Acknowledgments

- Semua developer program testnet yang dianalisis
- Blessed.js library untuk terminal UI inspiration
- ANSI escape code specifications
- Terminal emulator best practices

---

**Dibuat dengan ❤️ untuk komunitas testnet**
